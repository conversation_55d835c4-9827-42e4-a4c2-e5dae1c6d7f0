"""
文件: tmp.py
功能: 文本处理工具脚本，用于解析和处理句子
描述: 该脚本提供了一系列文本处理功能，主要用于处理和解析文本中的句子。
      主要功能包括：
      1. 处理省略号（根据上下文转换为句号或删除）
      2. 解析文本中的HTML/XML标签
      3. 处理引号内容，保护引号内的分隔符
      4. 根据分隔符将文本分割成句子
      
      脚本可用于预处理文本，以便进行后续的文本分析或语音合成任务。
"""
import json
import re

def process_ellipsis(text):
    """
    处理文本中的省略号
    
    根据上下文判断省略号的用途：
    - 如果是句子结束则替换为句号
    - 其他情况则删除省略号
    
    参数:
        text: 输入的文本字符串
        
    返回:
        处理后的文本字符串
    """
    # 找到所有省略号位置
    ellipsis_pattern = r'\.{3,}'
    
    def replace_ellipsis(match):
        start_pos = match.start()
        end_pos = match.end()
        
        # 检查省略号后面的内容
        remaining_text = text[end_pos:]
        
        # 情况1: 省略号在字符串末尾
        if not remaining_text.strip():
            return '.'
        
        # 情况2: 省略号后面跟空格+大写字母
        if re.match(r'\s+[A-Z]', remaining_text):
            return '.'
        
        # 情况3: 省略号后面跟空格+标点符号
        if re.match(r'\s+[.!?;。！？]', remaining_text):
            return '.'
        
        # 情况4: 省略号在引号内且引号后开始新内容
        # 检查省略号前面是否在引号内
        before_text = text[:start_pos]
        quote_count = before_text.count('"')
        if quote_count % 2 == 1:  # 在引号内
            # 检查引号后是否有新内容
            quote_end = remaining_text.find('"')
            if quote_end != -1:
                after_quote = remaining_text[quote_end + 1:].strip()
                if after_quote and (after_quote[0].isupper() or after_quote[0] in '['):
                    return '.'
        
        # 其他情况: 直接删除
        return ''
    
    return re.sub(ellipsis_pattern, replace_ellipsis, text)

def tts_sentence_parse(response: str):
    """
    将文本解析为句子列表
    
    根据句子分隔符和标签结构将文本分割成句子，同时处理：
    - HTML/XML标签
    - 引号内容
    - 省略号
    - 标点符号
    
    参数:
        response: 输入的文本字符串
        
    返回:
        句子列表，每个句子为一个字符串
    """
    # parse the sentence based on the sentence delimiters
    sentence_delims = ['\n', '\t', '.', ';', '!', '?', '。', '！', '？']
    
    # Delete if "</..>" structure exists in the response
    pattern = r"</[^\s>]+>"
    matches = re.findall(pattern, response)
    
    if matches:
        # Parse the response by such structure
        sentences = re.split(f"({pattern})", response)
        
        temp = []
        i = 0
        while i < len(sentences):
            if re.fullmatch(pattern, sentences[i]):
                i += 1 
                continue
            if i + 1 < len(sentences) and re.fullmatch(pattern, sentences[i+1]):
                temp.append(sentences[i] + sentences[i+1])
                i += 2
            else:
                temp.append(sentences[i])
                i += 1
        sentences = [s.strip() for s in temp if s.strip()]
        
        # For each of the sentence, we detect if the structure satisfy <a>...</a> structure, 
        # if it only have </a> but don't have <a>, we remove the </a>
        processed_sentences = []
        for s in sentences:  
            original_s = s  # 保留原句
        
            # 检查是否有结尾的 </xxx>
            closing_tag_match = re.search(r"</([^\s>]+)>\s*$", s)
            if closing_tag_match:
                tag_name = closing_tag_match.group(1)
                opening_tag = f"<{tag_name}>"
                if opening_tag not in s:
                    # 没有开头标签，则移除结尾标签
                    s = s[:closing_tag_match.start()]

            # 检查是否有开头的 <xxx>
            opening_tag_match = re.match(r"^\s*<([^\s>]+)>", s)
            if opening_tag_match:
                tag_name = opening_tag_match.group(1)
                closing_tag = f"</{tag_name}>"
                if closing_tag not in original_s:
                    # 没有结尾标签，则移除开头标签
                    s = re.sub(r"^\s*<" + re.escape(tag_name) + r">", "", s)

            s = process_ellipsis(s)
            processed_sentences.append(s.strip())
        
        sentences = processed_sentences
        
    else:

        def protect_quoted_content(text):
            """
            保护引号内的内容，防止分隔符在引号内被处理
            """
            quote_parts = []
            current_pos = 0
            in_quote = False
            quote_start = 0
            
            i = 0
            while i < len(text):
                if text[i] == '"':
                    if not in_quote:
                        quote_parts.append(text[current_pos:i])
                        quote_start = i
                        in_quote = True
                    else:
                        quoted_content = text[quote_start:i+1]
                        for j, delim in enumerate(sentence_delims):
                            quoted_content = quoted_content.replace(delim, f"<<DELIM_{j}_PLACEHOLDER>>")
                        quote_parts.append(quoted_content)
                        current_pos = i + 1
                        in_quote = False
                i += 1
            
            if current_pos < len(text):
                quote_parts.append(text[current_pos:])
            
            return ''.join(quote_parts)
        
        # 保护引号内容
        protected_response = protect_quoted_content(response)
        # 处理省略号
        protected_response = process_ellipsis(protected_response)
        
        # Parse the response by sentence delimiters
        delim_pattern = '|'.join(map(re.escape, sentence_delims))
        sentences = re.split(f'({delim_pattern})', protected_response)

        temp = []
        i = 0
        while i < len(sentences):
            part = sentences[i].strip()
            if i + 1 < len(sentences):
                part += sentences[i+1].strip()
                i += 2
            else:
                i += 1
            if part:
                temp.append(part)
        sentences = temp
        
        # 恢复引号内的分隔符
        for i, sentence in enumerate(sentences):
            for j, delim in enumerate(sentence_delims):
                sentence = sentence.replace(f"<<DELIM_{j}_PLACEHOLDER>>", delim)
            sentences[i] = sentence
    
    # 过滤空句子和只包含标点的句子
    sentences = [s for s in sentences if s.strip() and not re.match(r'^[\.!?;。！？\s]*$', s.strip())]
    
    return sentences

# 测试用例
test_cases = [
    '<Teasing>I said it\'s tradition and he said "tradition?".</Teasing><Seductive>We both... laughed so hard.</Seductive>',
    '<Sad>He said "I love you..." and then walked away.</Sad> <Confused>She was confused.</Confused>',
    '<Neutral>We went to the store... bought some milk... and came back home.</Neutral>',
    '<Hesitant>She whispered "I\'m not sure..." but then decided to go.</Hesitant>',
    '<Mysterious>The story continues... but I\'ll tell you later.</Mysterious>'
]

for test in test_cases:
    print(f"Input: {test}")
    sentences = tts_sentence_parse(test)
    for sen in sentences:
        print(f"  -> {sen}")
    print("-" * 100)