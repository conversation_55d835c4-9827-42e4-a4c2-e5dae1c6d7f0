"""
文件: check_duplicate_titles.py
功能: 检查JSON数据文件中的重复数据并提供去重功能
描述: 该脚本提供三个主要功能：
      1. 检查JSON文件中重复的"标题"字段（同时考虑"案号"字段）
      2. 检查完全相同的字典项（重复记录）
      3. 去除完全相同的重复字典项并保存到新文件
      
      脚本会分析重复情况并提供详细统计，包括重复率、重复类型等信息，
      对于完全重复的数据会自动进行去重处理，并保存为新文件。
"""
import json
from pathlib import Path
from collections import Counter

def check_duplicate_titles(file_path):
    """
    检查JSON文件中重复的"标题"字段
    """
    try:
        # 使用pathlib处理路径，避免转义字符问题
        path = Path(file_path)

        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        print(f"成功加载文件: {path}")
        print(f"总数据条数: {len(data)}")
        
        # 提取所有"标题"字段
        titles = []
        for i, item in enumerate(data):
            if isinstance(item, dict) and "标题" in item:
                if item['案号']:
                    title = item["标题"]+"###"+item["案号"]
                else:
                    title = item["标题"]+"###"+"null"
                if title:  # 排除空值
                    titles.append(title)
                else:
                    print(f"第{i+1}条数据的标题为空")
            else:
                print(f"第{i+1}条数据格式异常或缺少'标题'字段")
        
        print(f"有效标题数量: {len(titles)}")
        
        # 统计重复情况
        title_counts = Counter(titles)
        
        # 找出重复的标题
        duplicates = {title: count for title, count in title_counts.items() if count > 1}
        
        print(f"\n=== 重复标题分析 ===")
        print(f"总计重复的标题数量: {len(duplicates)}")
        print(f"涉及重复的数据条数: {sum(duplicates.values())}")
        
        if duplicates:
            print(f"\n重复标题详情:")
            sorted_duplicates = sorted(duplicates.items(), key=lambda x: x[1], reverse=True)
            
            for i, (title, count) in enumerate(sorted_duplicates, 1):
                print(f"{i}. 重复{count}次: {title[:100]}{'...' if len(title) > 100 else ''}")
                
        # 统计摘要
        unique_titles = len(title_counts)
        total_duplicated_entries = sum(duplicates.values()) - len(duplicates) if duplicates else 0
        
        print(f"\n=== 统计摘要 ===")
        print(f"唯一标题数量: {unique_titles}")
        print(f"重复标题种类: {len(duplicates)}")
        print(f"因重复产生的冗余条数: {total_duplicated_entries}")
        print(f"数据重复率: {total_duplicated_entries/len(data)*100:.2f}%")
        
        return {
            'total_records': len(data),
            'valid_titles': len(titles),
            'unique_titles': unique_titles,
            'duplicate_title_types': len(duplicates),
            'total_duplicated_entries': total_duplicated_entries,
            'duplicate_rate': total_duplicated_entries/len(data)*100,
            'duplicates': duplicates
        }
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def check_identical_dictionaries(file_path):
    """
    检查JSON文件中完全相同的字典
    """
    try:
        # 使用pathlib处理路径，避免转义字符问题
        path = Path(file_path)

        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        print(f"\n=== 完全相同字典检查 ===")
        print(f"总数据条数: {len(data)}")
        
        # 将字典转换为JSON字符串进行比较
        dict_strings = []
        for i, item in enumerate(data):
            if isinstance(item, dict):
                # 使用sort_keys=True确保字典顺序一致
                dict_str = json.dumps(item, sort_keys=True, ensure_ascii=False)
                dict_strings.append(dict_str)
            else:
                print(f"第{i+1}条数据不是字典格式")
        
        print(f"有效字典数量: {len(dict_strings)}")
        
        # 统计重复情况
        dict_counts = Counter(dict_strings)
        
        # 找出重复的字典
        duplicates = {dict_str: count for dict_str, count in dict_counts.items() if count > 1}
        
        print(f"完全相同的字典种类数量: {len(duplicates)}")
        print(f"涉及重复的数据条数: {sum(duplicates.values())}")
        
        if duplicates:
            print(f"\n完全重复字典详情:")
            sorted_duplicates = sorted(duplicates.items(), key=lambda x: x[1], reverse=True)
            
            for i, (dict_str, count) in enumerate(sorted_duplicates, 1):
                # 解析回字典以便更好地显示
                dict_obj = json.loads(dict_str)
                title = dict_obj.get('标题', '无标题')[:50]
                case_no = dict_obj.get('案号', '无案号')
                print(f"{i}. 重复{count}次: 标题={title}{'...' if len(dict_obj.get('标题', '')) > 50 else ''}, 案号={case_no}")
                
        # 统计摘要
        unique_dicts = len(dict_counts)
        total_duplicated_entries = sum(duplicates.values()) - len(duplicates) if duplicates else 0
        
        print(f"\n=== 完全重复字典统计摘要 ===")
        print(f"唯一字典数量: {unique_dicts}")
        print(f"重复字典种类: {len(duplicates)}")
        print(f"因完全重复产生的冗余条数: {total_duplicated_entries}")
        print(f"完全重复率: {total_duplicated_entries/len(data)*100:.2f}%")
        
        return {
            'total_records': len(data),
            'valid_dicts': len(dict_strings),
            'unique_dicts': unique_dicts,
            'duplicate_dict_types': len(duplicates),
            'total_duplicated_entries': total_duplicated_entries,
            'duplicate_rate': total_duplicated_entries/len(data)*100,
            'duplicates': duplicates
        }
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def remove_duplicate_dictionaries(file_path, output_path=None):
    """
    删除完全相同的字典，只保留一个，并保存到新文件
    """
    try:
        # 使用pathlib处理路径，避免转义字符问题
        path = Path(file_path)

        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        print(f"\n=== 开始去重处理 ===")
        print(f"原始数据条数: {len(data)}")
        
        # 用于存储唯一字典的集合和去重后的数据
        seen_dicts = set()
        unique_data = []
        removed_count = 0
        
        for i, item in enumerate(data):
            if isinstance(item, dict):
                # 使用sort_keys=True确保字典顺序一致
                dict_str = json.dumps(item, sort_keys=True, ensure_ascii=False)
                
                if dict_str not in seen_dicts:
                    # 第一次遇到这个字典，保留它
                    seen_dicts.add(dict_str)
                    unique_data.append(item)
                else:
                    # 这是重复的字典，跳过它
                    removed_count += 1
                    print(f"删除第{i+1}条重复数据: 标题={item.get('标题', '无标题')[:50]}...")
            else:
                # 不是字典格式，也保留
                unique_data.append(item)
                print(f"保留第{i+1}条非字典数据")
        
        print(f"去重完成!")
        print(f"删除了 {removed_count} 条完全重复的数据")
        print(f"去重后数据条数: {len(unique_data)}")
        
        # 确定输出文件路径
        if output_path is None:
            # 自动生成输出文件名
            input_stem = path.stem  # 获取不带扩展名的文件名
            input_suffix = path.suffix  # 获取文件扩展名
            output_path = path.parent / f"{input_stem}_去重{input_suffix}"
        
        output_path = Path(output_path)
        
        # 保存去重后的数据
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(unique_data, f, ensure_ascii=False, indent=2)
        
        print(f"去重后的数据已保存到: {output_path}")
        
        return {
            'original_count': len(data),
            'unique_count': len(unique_data),
            'removed_count': removed_count,
            'output_file': str(output_path),
            'reduction_rate': (removed_count / len(data)) * 100 if len(data) > 0 else 0
        }
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

if __name__ == "__main__":
    # 检查data_PCV.json文件
    print("=" * 60)
    print("开始检查重复数据...")
    print("=" * 60)
    
    # 1. 检查重复标题
    title_result = check_duplicate_titles("data_PCV.json")
    
    # 2. 检查完全相同的字典
    dict_result = check_identical_dictionaries("data_PCV.json")
    
    # 3. 如果有完全重复的字典，询问是否进行去重
    if dict_result and dict_result['duplicate_dict_types'] > 0:
        print(f"\n" + "=" * 60)
        print("发现完全重复的字典，开始自动去重...")
        print("=" * 60)
        
        # 执行去重操作
        dedup_result = remove_duplicate_dictionaries("data_PCV.json")
        
        if dedup_result:
            print(f"\n=== 去重结果 ===")
            print(f"原始数据: {dedup_result['original_count']} 条")
            print(f"去重后数据: {dedup_result['unique_count']} 条")
            print(f"删除重复: {dedup_result['removed_count']} 条")
            print(f"数据减少: {dedup_result['reduction_rate']:.2f}%")
            print(f"输出文件: {dedup_result['output_file']}")
    
    # 综合分析结果
    if title_result and dict_result:
        print(f"\n" + "=" * 60)
        print("综合处理建议")
        print("=" * 60)
        
        print(f"1. 标题重复分析:")
        if title_result['duplicate_title_types'] > 0:
            print(f"   - 发现 {title_result['duplicate_title_types']} 种重复标题")
            print(f"   - 可节省 {title_result['total_duplicated_entries']} 条重复数据")
            print(f"   - 标题重复率: {title_result['duplicate_rate']:.2f}%")
        else:
            print("   - 没有发现重复标题")
            
        print(f"\n2. 完全重复字典分析:")
        if dict_result['duplicate_dict_types'] > 0:
            print(f"   - 发现 {dict_result['duplicate_dict_types']} 种完全重复的字典")
            print(f"   - 可节省 {dict_result['total_duplicated_entries']} 条完全重复数据")
            print(f"   - 完全重复率: {dict_result['duplicate_rate']:.2f}%")
            print(f"   - 已自动处理完全重复的字典")
        else:
            print("   - 没有发现完全重复的字典")
            
        # 推荐处理策略
        print(f"\n3. 推荐处理策略:")
        if dict_result['duplicate_dict_types'] > 0:
            print(f"   - ✅ 完全重复的字典已自动去重")
            if title_result['duplicate_title_types'] > 0:
                print(f"   - ⚠️  标题重复的数据仍需人工审核")
        elif title_result['duplicate_title_types'] > 0:
            print(f"   - 处理标题重复的数据，需要人工审核")
            print(f"   - 标题去重后数据量: {title_result['total_records']} → {title_result['unique_titles']}")
        else:
            print("   - 数据质量良好，无需处理！") 