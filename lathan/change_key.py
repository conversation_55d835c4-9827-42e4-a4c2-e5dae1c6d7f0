"""
文件: change_key.py
功能: 处理JSON文件，提取每条数据中的案号信息，并将其添加到键名中
描述: 该脚本读取指定的JSON文件，从每条数据的响应文本中提取案号信息，
      然后创建新的键名格式为"原键###案号"，最终将处理后的数据保存为新的JSON文件。
      如果无法提取案号，会将相关信息记录到错误日志中。
"""
import os
import json
from tqdm import tqdm
import re

if __name__ == "__main__":
    results = {}
    with open('data_PCV_responser_data_deepseek_deepseek-chat-v3-0324.json', 'r', encoding='utf-8') as f:
        data = json.load(f)

    for key, value in tqdm(data.items()):
        response_text = value[0]['reponse_text']
        try:      
            case_num = json.loads(response_text)['案号']

            new_key = key+"###"+case_num

            results[new_key] = value
        except:
            
            try:
                pattern = r'"案号":\s*"([^"]+)"'
                case_number = re.search(pattern, response_text).group(1)

                new_key = key+"###"+case_number

                results[new_key] = value
                with open("tmp.txt", "a", encoding="utf-8") as f:
                    f.write(case_number+"\n")
            except:
                with open("error.txt", "a", encoding="utf-8") as f:
                    f.write(key+"\n")
    
    with open("new_key_name_deepseek_deepseek-chat-v3-0324.json", "w", encoding="utf-8") as file:
        json.dump(results, file, indent=4, ensure_ascii=False)