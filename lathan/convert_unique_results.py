"""
文件: convert_unique_results.py
功能: 将唯一键格式结果文件转换为标题格式
描述: 该脚本用于将基于唯一键（"标题###案号"格式）的结果文件转换为基于标题的格式，
      以便与现有的后处理脚本兼容。主要功能包括：
      1. 将唯一键格式转换为标题格式，处理重复标题的情况
      2. 分析结果文件的完整性，包括解析成功率统计
      3. 自动查找并处理工作目录中的所有唯一键结果文件
      
      脚本会自动生成转换后的文件，并提供详细的处理统计信息。
"""
import json
import os
from pathlib import Path

def convert_unique_key_results_to_title_format(unique_results_file, output_file=None):
    """
    将基于唯一键（标题+案号）的结果转换为基于标题的格式
    这样可以与现有的后处理脚本兼容
    """
    if not os.path.exists(unique_results_file):
        print(f"❌ 文件不存在: {unique_results_file}")
        return None
    
    try:
        # 加载唯一键结果
        with open(unique_results_file, 'r', encoding='utf-8') as f:
            unique_results = json.load(f)
        
        print(f"✅ 加载唯一键结果，共 {len(unique_results)} 条")
        
        # 转换为标题格式
        title_results = {}
        conversion_stats = {
            'processed': 0,
            'duplicates': 0,
            'errors': 0
        }
        
        for unique_key, result_data in unique_results.items():
            try:
                # 解析唯一键：标题###案号
                if '###' in unique_key:
                    title, case_number = unique_key.split('###', 1)
                else:
                    # 如果没有分隔符，使用整个键作为标题
                    title = unique_key
                    case_number = ''
                
                # 检查是否已存在该标题
                if title in title_results:
                    conversion_stats['duplicates'] += 1
                    print(f"⚠️ 重复标题: {title[:80]}...")
                    # 为重复标题创建新的键（添加案号后缀）
                    if case_number:
                        new_title = f"{title} [{case_number}]"
                        title_results[new_title] = result_data
                    else:
                        # 如果没有案号，添加序号
                        counter = 2
                        while f"{title} ({counter})" in title_results:
                            counter += 1
                        title_results[f"{title} ({counter})"] = result_data
                else:
                    title_results[title] = result_data
                
                conversion_stats['processed'] += 1
                
            except Exception as e:
                print(f"⚠️ 处理键时出错 {unique_key[:50]}...: {e}")
                conversion_stats['errors'] += 1
                continue
        
        # 生成输出文件名
        if not output_file:
            base_name = Path(unique_results_file).stem
            output_file = f"{base_name}_converted_to_title_format.json"
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(title_results, f, ensure_ascii=False, indent=4)
        
        print(f"\n✅ 转换完成！")
        print(f"📊 转换统计:")
        print(f"   - 处理条目: {conversion_stats['processed']}")
        print(f"   - 重复标题: {conversion_stats['duplicates']}")
        print(f"   - 错误条目: {conversion_stats['errors']}")
        print(f"   - 输出文件: {output_file}")
        
        return output_file
        
    except Exception as e:
        print(f"❌ 转换过程中出错: {e}")
        return None

def analyze_results_completeness(results_file):
    """
    分析结果文件的完整性
    """
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        total_items = len(results)
        successful_parses = 0
        parse_errors = 0
        
        print(f"\n📊 结果分析: {results_file}")
        print(f"总条目数: {total_items}")
        
        for key, result_data in results.items():
            try:
                if isinstance(result_data, list) and len(result_data) >= 1:
                    response_text = result_data[0].get('reponse_text', '')
                    if response_text:
                        # 尝试解析JSON
                        parsed_json = json.loads(response_text)
                        successful_parses += 1
                    else:
                        parse_errors += 1
                else:
                    parse_errors += 1
            except json.JSONDecodeError:
                parse_errors += 1
            except Exception:
                parse_errors += 1
        
        print(f"✅ 成功解析: {successful_parses} ({successful_parses/total_items*100:.1f}%)")
        print(f"❌ 解析错误: {parse_errors} ({parse_errors/total_items*100:.1f}%)")
        
        return {
            'total': total_items,
            'successful': successful_parses,
            'errors': parse_errors,
            'success_rate': successful_parses / total_items if total_items > 0 else 0
        }
        
    except Exception as e:
        print(f"❌ 分析文件时出错: {e}")
        return None

def find_unique_key_files():
    """
    查找所有唯一键结果文件
    """
    files = []
    for file_path in Path('.').glob('*_unique_keys.json'):
        files.append(str(file_path))
    return files

if __name__ == "__main__":
    print("🔄 唯一键结果转换工具")
    print("=" * 50)
    
    # 查找唯一键文件
    unique_files = find_unique_key_files()
    
    if not unique_files:
        print("❌ 未找到任何 *_unique_keys.json 文件")
        print("请先运行 process_unique_titles.py 生成结果文件")
        exit(1)
    
    print(f"📂 找到 {len(unique_files)} 个唯一键结果文件:")
    for i, file_path in enumerate(unique_files, 1):
        print(f"   {i}. {file_path}")
    
    # 处理每个文件
    for file_path in unique_files:
        print(f"\n🔄 处理文件: {file_path}")
        
        # 分析完整性
        stats = analyze_results_completeness(file_path)
        
        # 转换格式
        output_file = convert_unique_key_results_to_title_format(file_path)
        
        if output_file:
            print(f"✅ 转换完成: {output_file}")
        
        print("-" * 50)
    
    print("\n🎉 所有文件处理完成！") 