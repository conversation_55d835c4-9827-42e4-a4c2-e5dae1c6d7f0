"""
文件: process.py
功能: 处理法律判决书数据并使用AI模型进行分析
描述: 该脚本用于读取法律判决书数据，使用AI模型分析判决书内容，提取关键信息。
      主要功能包括：
      1. 将Excel格式数据转换为JSON格式
      2. 调用OpenRouter API进行文本分析（使用指定的AI模型）
      3. 处理和存储模型返回的分析结果
      4. 提取判决书中的关键信息，如案号、当事人、赔偿金额等
      
      脚本设计为处理批量数据，并支持断点续传（保存中间结果）
"""
import os
import sys
from pathlib import Path
import pandas as pd
import json
import requests
import tiktoken
import time
import random

def xlsx_to_json(file_path, output_file=None, encoding='utf-8', sample_num=None):
    """
    将Excel文件转换为JSON格式
    
    参数:
        file_path: Excel文件路径
        output_file: 输出的JSON文件路径，如不指定则使用Excel文件名
        encoding: 文件编码格式
        sample_num: 如指定，则只转换前N条记录
        
    返回:
        输出的JSON文件路径
    """
    
    if sample_num:
        df = pd.read_excel(file_path).head(sample_num)
    else:
        df = pd.read_excel(file_path)

    df = df.where(pd.notnull(df), None)

    
    data = df.to_dict('records')

    def replace_nan(obj):
        if isinstance(obj, float) and pd.isna(obj):
            return None
        elif isinstance(obj, dict):
            return {k: replace_nan(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [replace_nan(i) for i in obj]
        else:
            return obj

    data = replace_nan(data)

    if not output_file:
        output_file = file_path.replace('.xlsx', '.json')

    with open(output_file, 'w', encoding=encoding) as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    return output_file

def responser(user_message, system_prompt, model_name):
    """
    调用OpenRouter API进行文本分析
    
    参数:
        user_message: 用户输入的文本内容
        system_prompt: 系统提示词，指导AI模型的回答方向
        model_name: 使用的AI模型名称
        
    返回:
        content: 模型返回的内容
        cost: 调用API的费用
    """
    url = "https://openrouter.ai/api/v1/chat/completions"
    headers = {
        # "Authorization": "Bearer 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",
        # "Authorization": "Bearer 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",
        "Authorization": "Bearer 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",
        "HTTP-Referer": "http://localhost",
        "X-Title": "Context Analyzer"
    }

    data = {
        "model": model_name,
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
    }

    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()
        response_json = response.json()

        content = response_json["choices"][0]["message"]["content"]
        usage = response_json.get("usage", {})
        prompt_tokens = usage.get("prompt_tokens", 0)
        completion_tokens = usage.get("completion_tokens", 0)

        # Pricing per 1 million tokens
        cost = (prompt_tokens / 1000000) * 0.15 + (completion_tokens / 1000000) * 0.60

        return content, cost

    except requests.exceptions.HTTPError as e:
        if e.response.status_code == 429:
            print(f"Rate limit exceeded (429), sleeping for 5 seconds...")
            time.sleep(5)
            return responser(user_message, system_prompt, model_name)
        else:
            print(f"HTTP Error: {e}")
            return responser(user_message, system_prompt, model_name)
    except Exception as e:
        print(f"Error calling OpenRouter API: {e}")
        return responser(user_message, system_prompt, model_name)


from tqdm import tqdm

if __name__ == "__main__":
    system_prompt = """
    你是一位法律判决书的分析员。请根据下述审判文书判断，是否有证据表明被告滥用公司独立法人人格，被告是否逃避债务，或是造成债务人损失损害，最终法院是否要求被告应当承担无限连带责任？
    你需要在{"是否否认被告公司的独立法人人格，要求股东承担连带责任赔偿_GLM": null,}中填入是或者否，不需要返回你分析思考的过程部分。
    最后，你需要根据文书内容提取所有缺失的信息，填入字典中，并返回完整的，填补所有信息的字典。

    例如
        {
        "标题": "新余凤翔带钢有限公司与安徽省东波带钢有限公司、安徽省宁国市东波紧固件有限公司买卖合同纠纷一审民事判决书",
        "审理法院": "新余市渝水区人民法院",
        "法院信息_省": null,
        "法院信息_市": null,
        "法院信息_县": null,
        "法院等级": null,
        "案件类型": "民事案件",
        "案号": "（2015）渝民初字第02949号",
        "审理程序": "一审",
        "裁判日期": "2016-04-05",
        "发布日期": "2016-09-27",
        "文书内容": "本案由江西省新余市渝水区人民法院审理，审判长为甘丹丹，审判员为胡儿军、胡水根。原告新余凤翔带钢有限公司起诉被告安徽省东波带钢有限公司及安徽省宁国市东波紧固件有限公司，案由为买卖合同纠纷。原告诉请两被告支付货款943617.58元及利息13726.64元，并承担案件受理费、保全费共18374元。法院认定两被告在股东、高管、财务、办公地址等方面高度混同，否认其独立法人人格，裁定第二被告对第一被告的债务承担连带清偿责任。",
        "当事人": "新余凤翔带钢有限公司,安徽省东波带钢有限公司,安徽省宁国市东波紧固件有限公司",
        "原告": null,
        "被告": null,
        "案由": "买卖合同纠纷",
        "裁判年份": 2016,
        "裁判月份": 4,
        "是否否认被告公司的独立法人人格，要求股东承担连带责任赔偿_GLM": null,
        "审判长": null,
        "审判员": null,
        "原告要求赔偿金额": null
    },
    你只需要返回：
        {
        "标题": "新余凤翔带钢有限公司与安徽省东波带钢有限公司、安徽省宁国市东波紧固件有限公司买卖合同纠纷一审民事判决书",
        "审理法院": "新余市渝水区人民法院",
        "法院信息_省": "string",
        "法院信息_市": "string",
        "法院信息_县": "string",
        "法院等级": "string"（基层人民法院/中级人民法院/高级人民法院/最高人民法院）,
        "案件类型": "民事案件",
        "案号": "（2015）渝民初字第02949号",
        "审理程序": "一审",
        "裁判日期": "2016-04-05",
        "发布日期": "2016-09-27",
        "文书内容": "",（注意不需要返回文书内容）
        "当事人": "新余凤翔带钢有限公司,安徽省东波带钢有限公司,安徽省宁国市东波紧固件有限公司",
        "原告": "string",
        "被告": "string,string",（原告和被告如果是多个，用逗号分隔）
        "案由": "买卖合同纠纷",
        "裁判年份": 2016,
        "裁判月份": 4,
        "是否否认被告公司的独立法人人格，要求股东承担连带责任赔偿_GLM": "是/否",
        "审判长": "string",
        "审判员": "string、string",
        "原告要求赔偿金额": {
                    "compensation name": number,（赔偿金额及名称）
                    "compensation name": number,
                    "compensation name": number
                }
    },
    
"""
    # openai/gpt-4o-mini
    # deepseek/deepseek-chat-v3-0324
    model_name = input("请输入模型名称：")
    model_file_name = model_name.replace('/', '_').replace(':', '_')

    system_prompt = system_prompt.strip()

    file_path = 'data_PCV_去重.xlsx'
    filled_json_path = f'data_PCV_filled_data_{model_file_name}.json'
    reponser_json_path = f'new_key_name_{model_file_name}.json'

    
    # output_file = xlsx_to_json(file_path)
    output_file = f'data_PCV_去重.json'
    
    with open(output_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    if os.path.exists(reponser_json_path):
        with open(reponser_json_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
    else:
        results = {}
    
    for item in tqdm(data):
        try:
            key_name = item['标题']+"###"+item['案号']
        except:
            key_name = item['标题']
            print(key_name)
            
        if key_name not in results:
            result = []
            text = json.dumps(item, ensure_ascii=False)
            response_dict, cost = responser(text, system_prompt, model_name)
            response_dict = response_dict.replace('```json', '').replace('```', '')
            result.append({'reponse_text': response_dict})
            result.append({'cost': cost})
            results[item['标题']] = result
            with open(reponser_json_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=4)
            
            time.sleep(random.uniform(0.5, 1.5))
            
    


    # filled_data = {}
    # for key, value in tqdm(results.items()):
    #     json_response = value[0]['reponse_text']
    #     try:
    #         json_response = json.loads(json_response)
    #         filled_data[key] = json_response

    #         with open(filled_json_path, 'w', encoding='utf-8') as f:
    #             json.dump(filled_data, f, ensure_ascii=False, indent=4)

    #     except Exception as e:
    #         with open('error.txt', 'a', encoding='utf-8') as f:
    #             f.write(f"{key}: {e}\n")

    # df = pd.DataFrame(result)
    # output_xlsx = 'output_analysis.xlsx'
    # df.to_excel(output_xlsx, index=False)


