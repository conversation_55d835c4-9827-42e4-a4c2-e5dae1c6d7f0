"""
文件: json2xlsx.py
功能: 将JSON格式的案例数据转换为Excel格式
描述: 该脚本读取包含案例响应数据的JSON文件，并将其与原始案例数据合并。
      主要处理步骤包括：
      1. 读取AI模型生成的分析结果JSON文件
      2. 读取原始判决文书数据
      3. 将原始判决文书内容与AI分析结果合并
      4. 处理JSON格式的响应文本，并将其转换为结构化数据
      5. 最终将处理后的数据导出为Excel文件
      
      脚本会记录处理过程中的错误信息，以便后续分析和修复。
"""
import json
import pandas as pd

if __name__ == "__main__":

    with open('new_key_name_openai_gpt-4o-mini.json', 'r', encoding='utf-8') as f:

        data = json.load(f)
    
    with open("data_PCV.json", "r", encoding="utf-8") as f:
        data_pcv = json.load(f)
    
    pcv_judge_content = {}
    for item in data_pcv:
        try:
            key_name = item['标题']+"###"+item['案号']
            pcv_judge_content[key_name] = item['文书内容']
        except:
            key_name = item['标题']
            pcv_judge_content[key_name] = item['文书内容']
    
    
    
    xclx_results = []
    
    for key, value in data.items():
        try:
            json_response = value[0]['reponse_text']
            json_response = json.loads(json_response)
            json_response['文书内容'] = pcv_judge_content[key]
            xclx_results.append(json_response)


        except Exception as e:
            with open('json2xlsx_error_gpt.txt', 'a', encoding='utf-8') as f:
                f.write(f"{key}###{e}\n")
    
    df = pd.DataFrame(xclx_results)
    df.to_excel('gpt_xlsx_results.xlsx', index=False)
    


