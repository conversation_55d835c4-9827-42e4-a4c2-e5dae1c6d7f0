"""
文件: count_money.py
功能: 计算案件中的赔偿金额总和
描述: 该脚本读取Excel文件，解析其中的"原告要求赔偿金额"字段，
      计算每个案件中各项赔偿金额的总和，并将结果保存为新的Excel文件。
      脚本会尝试多种方式解析赔偿金额数据（包括JSON格式和Python字典格式），
      计算所有金额项的总和，并将结果添加为新列"赔偿总额"。
"""
import os
import sys
from pathlib import Path
import pandas as pd
import json
import requests
import tiktoken
import time
import random
import ast

def xlsx_to_json(file_path, output_file=None, encoding='utf-8', sample_num=None):
    """
    将Excel文件转换为JSON格式数据
    """
    
    if sample_num:
        df = pd.read_excel(file_path).head(sample_num)
    else:
        df = pd.read_excel(file_path)

    df = df.where(pd.notnull(df), None)

    
    data = df.to_dict('records')

    def replace_nan(obj):
        if isinstance(obj, float) and pd.isna(obj):
            return None
        elif isinstance(obj, dict):
            return {k: replace_nan(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [replace_nan(i) for i in obj]
        else:
            return obj

    data = replace_nan(data)

    if not output_file:
        output_file = file_path.replace('.xlsx', '.json')

    
    return data


if __name__ == "__main__":

    data = xlsx_to_json("deepseek_xlsx_results.xlsx")

    new_data = []
    for item in data:
        new_item = item.copy()
        money = item['原告要求赔偿金额']

        if isinstance(money, str):
            try:
                # Try JSON first
                money = json.loads(money)
            except json.JSONDecodeError:
                # If JSON fails, try to evaluate as Python literal (safer than eval)
                try:
                    money = ast.literal_eval(money)
                except (ValueError, SyntaxError):
                    print(f"Warning: Could not parse money string: {money}")
                    money = {}

        total = 0.0
        if money:
            for mon in money.values():
                if isinstance(mon, float):
                    total += mon
                elif isinstance(mon, int):
                    total += float(mon)
        else:
            total = 0.0

        new_item['赔偿总额'] = total
        new_data.append(new_item)
        
    df = pd.DataFrame(new_data)
    df.to_excel("new_key_name_deepseek_deepseek-chat-v3-0324_count_money.xlsx", index=False)
        
                






