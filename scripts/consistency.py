#!/usr/bin/env python3
"""
文件: consistency.py
功能: 计算模型判断结果的一致性
描述: 该脚本支持两种一致性分析：
      1. 单文件内：比较人工标注与模型标注的一致率
      2. 交叉验证：比较两个不同模型标注结果的一致率
"""
import json
import sys
import os

def calculate_consistency_single_file(file_path, original_field="法院是否支持原告否认被告的法人人格", 
                                     model_field="法院是否支持原告否认被告的法人人格_llm"):
    """计算单个文件中原始标注与模型标注的一致率"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_cases = 0
        consistent_cases = 0
        inconsistent_cases = []
        
        for item in data:
            # 获取两个字段的值
            original = item.get(original_field)
            model_result = item.get(model_field)
            case_number = item.get("案号", "未知案号")
            
            # 跳过没有原始标注或模型结果的案例
            if not original or not model_result:
                continue
            
            total_cases += 1
            
            # 比较两个字段是否一致
            if original == model_result:
                consistent_cases += 1
            else:
                inconsistent_cases.append(case_number)
        
        # 计算一致率
        consistency = consistent_cases / total_cases if total_cases > 0 else 0
        
        return {
            "总案例数": total_cases,
            "一致案例数": consistent_cases,
            "一致率": consistency,
            "不一致案例": inconsistent_cases
        }
    
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def calculate_consistency_cross_validation(file_path1, file_path2, field_name="法院是否支持原告否认被告的法人人格_llm"):
    """比较两个模型文件之间的标注一致率"""
    try:
        # 读取两个文件
        with open(file_path1, 'r', encoding='utf-8') as f1:
            data1 = json.load(f1)
        with open(file_path2, 'r', encoding='utf-8') as f2:
            data2 = json.load(f2)
        
        # 创建案号到结果的映射
        case_map1 = {item.get("案号"): item.get(field_name) for item in data1 if item.get("案号") and item.get(field_name)}
        case_map2 = {item.get("案号"): item.get(field_name) for item in data2 if item.get("案号") and item.get(field_name)}
        
        # 找出两个文件都有的案例
        common_cases = set(case_map1.keys()) & set(case_map2.keys())
        total_cases = len(common_cases)
        
        # 找出只存在于一个文件中的案例
        only_in_file1 = set(case_map1.keys()) - set(case_map2.keys())
        only_in_file2 = set(case_map2.keys()) - set(case_map1.keys())
        
        consistent_cases = 0
        inconsistent_cases = []
        
        # 比较共有案例的结果
        for case in common_cases:
            if case_map1[case] == case_map2[case]:
                consistent_cases += 1
            else:
                inconsistent_cases.append(case)
        
        # 计算一致率
        consistency = consistent_cases / total_cases if total_cases > 0 else 0
        
        return {
            "共有案例数": total_cases,
            "一致案例数": consistent_cases,
            "一致率": consistency,
            "不一致案例": inconsistent_cases,
            "只在文件1中存在": list(only_in_file1),
            "只在文件2中存在": list(only_in_file2)
        }
    
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return None

def print_result(result, analysis_type):
    """打印分析结果"""
    if not result:
        return
    
    if analysis_type == "single":
        print(f"一致率: {result['一致率']:.2%} ({result['一致案例数']}/{result['总案例数']})")
    else:  # cross
        print(f"交叉验证一致率: {result['一致率']:.2%} ({result['一致案例数']}/{result['共有案例数']})")
    
    # if result.get('不一致案例'):
    #     print("\n不一致的案号:")
    #     for case_number in result['不一致案例']:
    #         print(f"{case_number}")
    
    # 打印只存在于一个文件中的案例
    if analysis_type == "cross":
        if result.get('只在文件1中存在'):
            print("\n只存在于文件1中的案号:")
            for case_number in result['只在文件1中存在']:
                print(f"{case_number}")
        
        if result.get('只在文件2中存在'):
            print("\n只存在于文件2中的案号:")
            for case_number in result['只在文件2中存在']:
                print(f"{case_number}")

def main():
    if len(sys.argv) < 2:
        print("用法: python consistency.py <分析模式> [参数...]")
        print("分析模式:")
        print("  single <文件路径> [原始字段名] [模型字段名]  - 单文件内一致性分析")
        print("  cross <文件1路径> <文件2路径> [比较字段名]  - 两个文件间交叉验证")
        return
    
    mode = sys.argv[1]
    
    if mode == "single":
        if len(sys.argv) < 3:
            print("用法: python consistency.py single <文件路径> [原始字段名] [模型字段名]")
            return
            
        file_path = sys.argv[2]
        original_field = sys.argv[3] if len(sys.argv) > 3 else "法院是否支持原告否认被告的法人人格"
        model_field = sys.argv[4] if len(sys.argv) > 4 else "法院是否支持原告否认被告的法人人格_llm"
        
        result = calculate_consistency_single_file(file_path, original_field, model_field)
        print_result(result, "single")
    
    elif mode == "cross":
        if len(sys.argv) < 4:
            print("用法: python consistency.py cross <文件1路径> <文件2路径> [比较字段名]")
            return
            
        file_path1 = sys.argv[2]
        file_path2 = sys.argv[3]
        field_name = sys.argv[4] if len(sys.argv) > 4 else "法院是否支持原告否认被告的法人人格"
        
        result = calculate_consistency_cross_validation(file_path1, file_path2, field_name)
        print_result(result, "cross")
    
    else:
        print("错误: 未知的分析模式")
        print("支持的模式: single, cross")

if __name__ == "__main__":
    main()
