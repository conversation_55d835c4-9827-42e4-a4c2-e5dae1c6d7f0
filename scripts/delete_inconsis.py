import json
import os

def load_case_numbers_from_txt(txt_file):
    """
    从txt文件中读取案号列表
    
    Args:
        txt_file: txt文件路径
        
    Returns:
        set: 案号集合
    """
    try:
        with open(txt_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 清理每行数据，去除空白字符
        case_numbers = set()
        for line in lines:
            case_num = line.strip()
            if case_num:  # 跳过空行
                case_numbers.add(case_num)
        
        return case_numbers
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {txt_file}")
        return None
    except Exception as e:
        print(f"读取txt文件时出错: {e}")
        return None

def delete_cases_from_json(json_file, case_numbers_to_delete, output_file=None):
    """
    从JSON文件中删除指定案号的条目
    
    Args:
        json_file: JSON文件路径
        case_numbers_to_delete: 要删除的案号集合
        output_file: 输出文件路径，如果为None则覆盖原文件
        
    Returns:
        tuple: (删除的条目数, 剩余的条目数)
    """
    try:
        # 读取JSON文件
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        original_count = len(data)
        print(f"原始数据共有 {original_count} 条")
        print(f"要删除的案号共有 {len(case_numbers_to_delete)} 个")
        
        # 过滤数据，保留不在删除列表中的条目
        filtered_data = []
        deleted_count = 0
        
        for item in data:
            if "案号" in item:
                if item["案号"] in case_numbers_to_delete:
                    deleted_count += 1
                    print(f"删除案号: {item['案号']}")
                else:
                    filtered_data.append(item)
            else:
                # 如果没有案号字段，保留该条目
                filtered_data.append(item)
                print(f"警告: 某条数据缺少案号字段，已保留")
        
        # 确定输出文件路径
        if output_file is None:
            output_file = json_file
        
        # 写入处理后的数据
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(filtered_data, f, ensure_ascii=False, indent=2)
        
        remaining_count = len(filtered_data)
        
        print(f"\n处理完成:")
        print(f"- 删除了 {deleted_count} 条数据")
        print(f"- 剩余 {remaining_count} 条数据")
        print(f"- 结果已保存到: {output_file}")
        
        return deleted_count, remaining_count
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file}")
        return None, None
    except json.JSONDecodeError:
        print(f"错误: 文件 {json_file} 不是有效的JSON格式")
        return None, None
    except Exception as e:
        print(f"处理JSON文件时出错: {e}")
        return None, None

def main():
    """主函数"""
    print("JSON条目删除工具")
    print("=" * 40)
    
    # 获取输入文件路径
    txt_file = input("请输入包含案号的txt文件路径: ").strip()
    if not txt_file:
        print("错误: 请输入有效的txt文件路径")
        return
    
    # 检查txt文件是否存在
    if not os.path.exists(txt_file):
        print(f"错误: 找不到文件 {txt_file}")
        return
    
    # 读取要删除的案号
    case_numbers_to_delete = load_case_numbers_from_txt(txt_file)
    if case_numbers_to_delete is None:
        return
    
    if not case_numbers_to_delete:
        print("警告: txt文件中没有找到任何案号")
        return
    
    # 获取JSON文件路径
    json_file = input("请输入JSON文件路径: ").strip()
    if not json_file:
        print("错误: 请输入有效的JSON文件路径")
        return
    
    # 检查JSON文件是否存在
    if not os.path.exists(json_file):
        print(f"错误: 找不到文件 {json_file}")
        return
    
    # 询问是否保存到新文件
    save_new = input("是否保存到新文件？(y/n，默认覆盖原文件): ").lower()
    output_file = None
    if save_new == 'y':
        output_file = input("请输入输出文件路径: ").strip()
        if not output_file:
            # 自动生成输出文件名
            base_name = os.path.splitext(json_file)[0]
            output_file = f"{base_name}_filtered.json"
            print(f"使用默认输出文件名: {output_file}")
    
    # 确认操作
    print(f"\n即将执行:")
    print(f"- 从 {json_file} 中删除 {len(case_numbers_to_delete)} 个案号对应的条目")
    if output_file:
        print(f"- 结果保存到 {output_file}")
    else:
        print(f"- 覆盖原文件 {json_file}")
    
    confirm = input("\n确认执行？(y/n): ").lower()
    if confirm != 'y':
        print("操作已取消")
        return
    
    # 执行删除操作
    deleted_count, remaining_count = delete_cases_from_json(
        json_file, case_numbers_to_delete, output_file
    )
    
    if deleted_count is not None:
        print("\n操作成功完成！")

if __name__ == "__main__":
    main()
