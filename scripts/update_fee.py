import json

SOURCE_DATA = "ds_random_500.json"
TARGET_DATA = "ds_statistic_fee.json"

def update_json_field():
    # 读取源文件和目标文件
    with open(SOURCE_DATA, "r", encoding="utf-8") as f:
        source_data = json.load(f)
    
    with open(TARGET_DATA, "r", encoding="utf-8") as f:
        target_data = json.load(f)
    
    # 创建源文件的案号映射表
    source_map = {}
    for item in source_data:
        if "案号" in item:
            case_number = item["案号"]
            update_fields = {
                "原告要求赔偿金额": item.get("原告要求赔偿金额", {}),
                "最终判决赔偿金额": item.get("最终判决赔偿金额", {})
            }
            source_map[case_number] = update_fields
    
    # 记录更新的条目数和案号列表
    updated_count = 0
    updated_cases = []
    
    # 遍历目标文件中的条目进行更新
    for item in target_data:
        if "案号" in item:
            case_number = item["案号"]
            
            if case_number in source_map:
                updated_count += 1
                source_fields = source_map[case_number]
                
                item["原告要求赔偿金额"] = source_fields["原告要求赔偿金额"]
                item["最终判决赔偿金额"] = source_fields["最终判决赔偿金额"]

    
    # 保存更新后的目标文件
    with open(TARGET_DATA, "w", encoding="utf-8") as f:
        json.dump(target_data, f, ensure_ascii=False, indent=4)
    
    # 输出更新结果
    print(f"总共更新了 {updated_count} 个条目")
    print("更新的案号列表:")
    for case in updated_cases:
        print(case)
    
    return updated_count, updated_cases

if __name__ == "__main__":
    update_json_field()
