#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import argparse
import random
from typing import Dict, List, Tuple, Any, Optional, Set

OUTPUT_TXT = "inconsistent_cases_no.txt"

def is_consistent(value1: Optional[float], value2: Optional[float], tolerance: float = 1000.0) -> bool:
    """
    判断两个金额是否一致，允许有一定的误差范围
    
    Args:
        value1: 第一个金额
        value2: 第二个金额
        tolerance: 允许的误差范围，默认为10元
        
    Returns:
        bool: 如果两个金额在误差范围内一致，返回True；否则返回False
    """
    if value1 is None and value2 is None:
        return True
    if value1 is None or value2 is None:
        return False
    
    tolerance = max(value1, value2) * 0.05
    return abs(value1 - value2) <= tolerance


def load_json_file(file_path: str) -> List[Dict[str, Any]]:
    """
    加载JSON文件
    
    Args:
        file_path: JSON文件路径
        
    Returns:
        List[Dict[str, Any]]: JSON数据列表
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def compare_fee_consistency(file1: str, file2: str) -> Tuple[Dict[str, Tuple[int, int]], Dict[str, Tuple[int, int]], Dict[str, List[str]]]:
    """
    比较两个JSON文件中的赔偿金额一致性
    
    Args:
        file1: 第一个JSON文件路径
        file2: 第二个JSON文件路径
        
    Returns:
        Tuple[Dict[str, Tuple[int, int]], Dict[str, Tuple[int, int]], Dict[str, List[str]]]: 
            第一个字典包含总体一致性结果，
            第二个字典包含仅数值一致性结果，
            第三个字典包含不一致案号（原告和判决分开）
    """
    # 加载JSON文件
    data1 = load_json_file(file1)
    data2 = load_json_file(file2)
    
    # 将数据按案号组织成字典
    data1_dict = {item['案号']: item for item in data1 if '案号' in item}
    data2_dict = {item['案号']: item for item in data2 if '案号' in item}
    
    # 获取两个文件中共有的案号
    common_case_numbers = set(data1_dict.keys()) & set(data2_dict.keys())
    total_cases = len(common_case_numbers)
    
    # 初始化计数器和不一致案号集合
    plaintiff_consistent = 0
    judgment_consistent = 0
    plaintiff_value_exists = 0
    judgment_value_exists = 0
    plaintiff_value_consistent = 0
    judgment_value_consistent = 0
    
    # 存储不一致的案号
    # inconsistent_cases = {
    #     '原告要求金额': set(),
    #     '最终判决金额': set()
    # }
    inconsistent_amount = set()   
    
    # 比较每个案件的赔偿金额
    for case_number in common_case_numbers:
        case1 = data1_dict[case_number]
        case2 = data2_dict[case_number]
        
        # 获取原告要求赔偿金额
        plaintiff_req1 = case1.get('原告要求赔偿金额')
        plaintiff_amount1 = plaintiff_req1.get('总金额') if isinstance(plaintiff_req1, dict) else None
        
        plaintiff_req2 = case2.get('原告要求赔偿金额')
        plaintiff_amount2 = plaintiff_req2.get('总金额') if isinstance(plaintiff_req2, dict) else None
        
        # 获取最终判决赔偿金额
        judgment_req1 = case1.get('最终判决赔偿金额')
        judgment_amount1 = judgment_req1.get('总金额') if isinstance(judgment_req1, dict) else None
        
        judgment_req2 = case2.get('最终判决赔偿金额')
        judgment_amount2 = judgment_req2.get('总金额') if isinstance(judgment_req2, dict) else None
        
        # 判断原告要求赔偿金额是否一致
        if is_consistent(plaintiff_amount1, plaintiff_amount2):
            plaintiff_consistent += 1
        # elif plaintiff_amount1 is not None and plaintiff_amount2 is not None:
        #     inconsistent_cases['原告要求金额'].add(case_number)
        
        # 判断最终判决赔偿金额是否一致
        if is_consistent(judgment_amount1, judgment_amount2):
            judgment_consistent += 1
        # elif judgment_amount1 is not None and judgment_amount2 is not None:
        #     inconsistent_cases['最终判决金额'].add(case_number)
        
        # # 判断原告要求赔偿金额是否都存在数值
        # if plaintiff_amount1 is not None and plaintiff_amount2 is not None:
        #     plaintiff_value_exists += 1
        #     if is_consistent(plaintiff_amount1, plaintiff_amount2):
        #         plaintiff_value_consistent += 1
        
        # # 判断最终判决赔偿金额是否都存在数值
        # if judgment_amount1 is not None and judgment_amount2 is not None:
        #     judgment_value_exists += 1
        #     if is_consistent(judgment_amount1, judgment_amount2):
        #         judgment_value_consistent += 1
      
        # 将因为各种原因导致不一致的案号都进行保存（我自己写的！）        
        if not is_consistent(plaintiff_amount1, plaintiff_amount2):
            inconsistent_amount.add(case_number)
        elif not is_consistent(judgment_amount1, judgment_amount2):
            inconsistent_amount.add(case_number)
        
    
    # 返回结果
    overall_results = {
        '原告要求金额': (plaintiff_consistent, total_cases),
        '最终判决金额': (judgment_consistent, total_cases)
    }
    
    # value_only_results = {
    #     '原告要求金额': (plaintiff_value_consistent, plaintiff_value_exists),
    #     '最终判决金额': (judgment_value_consistent, judgment_value_exists)
    # }
    
    # 将集合转换为列表
    # inconsistent_case_lists = {
    #     '原告要求金额': list(inconsistent_cases['原告要求金额']),
    #     '最终判决金额': list(inconsistent_cases['最终判决金额'])
    # }
    
    inconsistent_case_lists = {
        '不一致案号': list(inconsistent_amount)
    }
    
    return overall_results, inconsistent_case_lists
    # return overall_results, value_only_results, inconsistent_case_lists


def format_percentage(numerator: int, denominator: int) -> str:
    """
    格式化百分比
    
    Args:
        numerator: 分子
        denominator: 分母
        
    Returns:
        str: 格式化后的百分比字符串
    """
    if denominator == 0:
        return "0%(0/0)"
    percentage = (numerator / denominator) * 100
    return f"{percentage:.2f}%({numerator}/{denominator})"


def print_inconsistent_cases(inconsistent_cases: Dict[str, List[str]], max_cases: int = 50):
    """
    打印不一致的案号
    
    Args:
        inconsistent_cases: 不一致案号字典
        max_cases: 最大打印数量
    """
    print("\n==== 不一致案号样本 ====")
    
    for category, ases in inconsistent_cases.items():
        print(f"\n{category}不一致案号（共{len(cases)}个，随机显示{min(max_cases, len(cases))}个）:")
        if cases:
            for case in random.sample(cases, min(max_cases, len(cases))):
                print(f" - {case}")
        else:
            print(" 无")


def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='比较两个JSON文件中的赔偿金额一致性')
    parser.add_argument('file1', help='第一个JSON文件路径')
    parser.add_argument('file2', help='第二个JSON文件路径')
    args = parser.parse_args()
    
    # 比较赔偿金额一致性
    # overall_results, value_only_results, inconsistent_cases = compare_fee_consistency(args.file1, args.file2)
    overall_results, inconsistent_cases = compare_fee_consistency(args.file1, args.file2)
    
    # 输出结果
    print(f"原告要求金额一致率: {format_percentage(*overall_results['原告要求金额'])}")
    print(f"最终判决金额一致率: {format_percentage(*overall_results['最终判决金额'])}")
    # print(f"原告要求金额一致率（仅数值）: {format_percentage(*value_only_results['原告要求金额'])}")
    # print(f"最终判决金额一致率（仅数值）: {format_percentage(*value_only_results['最终判决金额'])}")
    
    
    # # 输出不一致案号
    # print_inconsistent_cases(inconsistent_cases)
    print(len(inconsistent_cases['不一致案号']))
    # 将不一致案号写入txt文件
    with open(OUTPUT_TXT, "w", encoding="utf-8") as f:
        for case_number in inconsistent_cases['不一致案号']:
            f.write(f"{case_number}\n")
    print(f"已将不一致案号写入 {OUTPUT_TXT}")


if __name__ == "__main__":
    main()