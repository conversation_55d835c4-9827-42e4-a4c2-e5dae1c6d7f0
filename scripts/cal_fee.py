#!/usr/bin/env python3
"""
文件: cal_fee.py
用法: python cal_fee.py <输入文件路径> <输出文件路径>
功能: 计算案件中的赔偿金额总和
描述: 该脚本读取JSON格式的案例数据，分别计算"原告要求赔偿金额"和"最终判决赔偿金额"字段中
      数值型条目的总和，并将总和作为"总金额"字段添加到相应对象中
      其中为空的字段设为{"总金额": 0.0}
"""
import json
import sys
import os
from tqdm import tqdm

def calculate_total_amount(amount_obj):
    """计算金额对象中的总金额（仅计算数值型字段）"""
    if not amount_obj or not isinstance(amount_obj, dict):
        return 0.0  # 修改为返回0.0而不是None
    
    total = 0.0
    for key, value in amount_obj.items():
        if key == "总金额":  # 跳过已存在的总金额字段
            continue
        # 只累加数值型字段
        if isinstance(value, (int, float)) and value is not None:
            total += float(value)
    
    return total

def process_file(input_file, output_file):
    """处理JSON文件，计算并添加总金额字段"""
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"成功加载数据，共 {len(data)} 条记录")
        
        # 统计计数器
        processed_count = 0
        plaintiff_amount_count = 0
        judgment_amount_count = 0
        
        # 处理每条记录
        for item in tqdm(data, desc="处理记录"):
            # 处理原告要求赔偿金额
            if "原告要求赔偿金额" not in item or not item["原告要求赔偿金额"]:
                item["原告要求赔偿金额"] = {"总金额": 0.0}
            else:
                total = calculate_total_amount(item["原告要求赔偿金额"])
                item["原告要求赔偿金额"]["总金额"] = total
                plaintiff_amount_count += 1
            
            # 处理最终判决赔偿金额
            if "最终判决赔偿金额" not in item or not item["最终判决赔偿金额"]:
                item["最终判决赔偿金额"] = {"总金额": 0.0}
            else:
                total = calculate_total_amount(item["最终判决赔偿金额"])
                item["最终判决赔偿金额"]["总金额"] = total
                judgment_amount_count += 1
            
            processed_count += 1
        
        # 将处理后的数据写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"处理完成！")
        print(f"处理记录总数: {processed_count}")
        print(f"原告要求赔偿不为空的字段数: {plaintiff_amount_count}")
        print(f"最终判决赔偿不为空的字段数: {judgment_amount_count}")
        print(f"结果已保存至: {output_file}")
        
    except Exception as e:
        print(f"处理文件时出错: {e}")
        return False
    
    return True

def main():
    if len(sys.argv) < 3:
        print("用法: python cal_fee.py <输入文件路径> <输出文件路径>")
        return
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return
    
    # 确保输出文件的目录存在
    output_dir = os.path.dirname(output_file)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    process_file(input_file, output_file)

if __name__ == "__main__":
    main()
