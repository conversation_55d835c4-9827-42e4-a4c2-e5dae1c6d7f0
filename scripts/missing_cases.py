#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脚本: missing_cases.py
功能: 比较原始数据文件和处理后的文件，找出缺失的案例
      以"案号"字段作为主键进行比较
输出: 包含缺失案号列表的TXT文件
"""

import json
import os
import sys


def find_missing_cases(original_file, processed_file, output_file):
    """
    比较两个JSON文件，找出在原始文件中存在但在处理后文件中缺失的案号
    
    参数:
        original_file: 原始数据文件路径
        processed_file: 处理后的文件路径
        output_file: 输出的TXT文件路径（保存缺失案号）
    
    返回:
        missing_count: 缺失案例的数量
    """
    try:
        # 读取原始JSON文件
        with open(original_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        print(f"成功加载原始数据，共 {len(original_data)} 条记录")
        
        # 读取处理后的JSON文件
        with open(processed_file, 'r', encoding='utf-8') as f:
            processed_data = json.load(f)
        print(f"成功加载处理后数据，共 {len(processed_data)} 条记录")
        
        # 提取两个文件中的案号集合
        original_case_numbers = set()
        for item in original_data:
            case_number = item.get("案号")
            if case_number:
                original_case_numbers.add(case_number)
        
        processed_case_numbers = set()
        for item in processed_data:
            case_number = item.get("案号")
            if case_number:
                processed_case_numbers.add(case_number)
        
        # 找出缺失的案号
        missing_cases = original_case_numbers - processed_case_numbers
        
        # 输出缺失案号到文本文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"缺失案例数量: {len(missing_cases)}\n\n")
            f.write("缺失案例案号列表:\n")
            for case_number in sorted(missing_cases):
                f.write(f"{case_number}\n")
        
        print(f"发现 {len(missing_cases)} 条缺失记录，已保存至 {output_file}")
        return len(missing_cases)
    
    except Exception as e:
        print(f"处理数据时发生错误: {e}")
        return -1


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) < 3:
        print("用法: python missing_cases.py <原始数据文件> <处理后数据文件> [输出TXT文件]")
        print("示例: python scripts/missing_cases.py data_PCV.json 4.1_full_judge.json missing_cases.txt")
        sys.exit(1)
    
    # 获取文件路径
    original_file = sys.argv[1]
    processed_file = sys.argv[2]
    
    # 如果未提供输出文件路径，则使用默认名称
    if len(sys.argv) >= 4:
        output_file = sys.argv[3]
    else:
        output_file = "missing_cases.txt"
    
    # 检查文件是否存在
    if not os.path.exists(original_file):
        print(f"错误: 原始数据文件 {original_file} 不存在")
        sys.exit(1)
    
    if not os.path.exists(processed_file):
        print(f"错误: 处理后数据文件 {processed_file} 不存在")
        sys.exit(1)
    
    print(f"正在比较文件:\n原始数据: {original_file}\n处理后数据: {processed_file}")
    missing_count = find_missing_cases(original_file, processed_file, output_file)
    
    if missing_count >= 0:
        print(f"比较完成！共发现 {missing_count} 条缺失记录")
        print(f"缺失案例案号已保存至: {output_file}")
    else:
        print("比较失败，请检查错误信息")
