#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
脚本: judge_failed.py
功能: 检查JSON文件中"法院是否支持原告否认被告的法人人格"字段值是否合法(仅允许"Y"或"N")
      将不合法条目的案号输出到文本文件，并从原JSON中删除不合法条目
输出: 1. 不合法条目案号的TXT文件
      2. 删除不合法条目后的新JSON文件
"""

import json
import os
import sys


def check_and_remove_invalid_entries(input_file, output_txt_file, output_json_file=None):
    """
    检查JSON文件中的不合法条目并删除它们
    
    参数:
        input_file: 输入的JSON文件路径
        output_txt_file: 输出的TXT文件路径（保存不合法条目案号）
        output_json_file: 输出的JSON文件路径（保存清理后的数据）
    
    返回:
        invalid_count: 不合法条目的数量
    """
    try:
        # 读取JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"成功加载数据，共 {len(data)} 条记录")
        
        # 统计不合法条目
        invalid_entries = []
        valid_entries = []
        
        for item in data:
            case_number = item.get("案号", "未知案号")
            support_value = item.get("法院是否支持原告否认被告的法人人格")
            
            # 检查值是否为"Y"或"N"
            if support_value != "Y" and support_value != "N":
                invalid_entries.append({
                    "案号": case_number,
                    "值": str(support_value)
                })
            else:
                valid_entries.append(item)
        
        # 输出不合法条目的案号到文本文件
        with open(output_txt_file, 'w', encoding='utf-8') as f:
            f.write(f"不合法条目数量: {len(invalid_entries)}\n\n")
            f.write("不合法条目案号列表:\n")
            for entry in invalid_entries:
                f.write(f"{entry['案号']}\n")
        
        # 如果指定了输出JSON文件，保存清理后的数据
        if output_json_file:
            with open(output_json_file, 'w', encoding='utf-8') as f:
                json.dump(valid_entries, f, ensure_ascii=False, indent=2)
            print(f"清理后的数据已保存至 {output_json_file}，共 {len(valid_entries)} 条记录")
        
        print(f"发现 {len(invalid_entries)} 条不合法记录，已保存至 {output_txt_file}")
        return len(invalid_entries)
    
    except Exception as e:
        print(f"处理数据时发生错误: {e}")
        return -1


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("用法: python judge_failed.py <input_json_file> [output_txt_file] [output_json_file]")
        print("示例: python judge_failed.py ds_full_judge.json ds_judge_failed.txt ds_full_judge_cleaned.json")
        sys.exit(1)
    
    # 获取输入文件路径
    input_file = sys.argv[1]
    
    # 如果未提供输出文件路径，则基于输入文件名生成
    if len(sys.argv) >= 3:
        output_txt_file = sys.argv[2]
    else:
        output_txt_file = "4.1_judge_failed.txt"
    
    # 如果提供了输出JSON文件路径
    output_json_file = None
    if len(sys.argv) >= 4:
        output_json_file = sys.argv[3]
    else:
        # 从输入文件名生成输出JSON文件名
        input_basename = os.path.basename(input_file)
        input_name = os.path.splitext(input_basename)[0]
        output_json_file = f"{input_name}_cleaned.json"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        sys.exit(1)
    
    print(f"正在分析文件: {input_file}")
    invalid_count = check_and_remove_invalid_entries(input_file, output_txt_file, output_json_file)
    
    if invalid_count >= 0:
        print(f"分析完成！共发现并删除 {invalid_count} 条不合法记录")
        print(f"不合法条目案号已保存至: {output_txt_file}")
        print(f"清理后的数据已保存至: {output_json_file}")
    else:
        print("分析失败，请检查错误信息")
