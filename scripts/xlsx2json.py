"""
文件: xlsx2json.py
功能: 将Excel文件转换为JSON格式
描述: 该脚本读取指定的Excel文件，并将其转换为JSON格式保存。
      主要功能包括：
      1. 读取Excel文件的数据
      2. 处理数据中的空值(NaN)
      3. 基于"案号"字段去除重复记录
      4. 将处理后的数据保存为JSON格式
"""
import pandas as pd
import json
from pathlib import Path

def xlsx_to_json(file_path, output_file=None, encoding='utf-8', sample_num=None):
    """
    将Excel文件转换为JSON格式
    
    参数:
        file_path: Excel文件路径
        output_file: 输出的JSON文件路径，如不指定则使用Excel文件名
        encoding: 文件编码格式
        sample_num: 如指定，则只转换前N条记录
        
    返回:
        输出的JSON文件路径
    """
    print(f"正在读取Excel文件: {file_path}")
    
    if sample_num:
        df = pd.read_excel(file_path).head(sample_num)
    else:
        df = pd.read_excel(file_path)

    print(f"成功读取数据，共 {len(df)} 行")
    
    # 将NaN值替换为None
    df = df.where(pd.notnull(df), None)
    
    # 检查是否有"案号"列，执行去重前统计重复案号
    if "案号" in df.columns:
        # 统计案号出现次数
        counts = df["案号"].value_counts()
        duplicates = counts[counts > 1]
        if not duplicates.empty:
            print("发现以下重复案号及其出现次数：")
            for case_id, count in duplicates.items():
                print(f"案号: {case_id}，出现次数: {count}")
        else:
            print("未发现重复案号。")
        original_count = len(df)
        # 去除重复的"案号"记录，保留第一次出现的记录
        df = df.drop_duplicates(subset=["案号"], keep="first")
        removed_count = original_count - len(df)
        if removed_count > 0:
            print(f"已去除 {removed_count} 条重复案号的记录，剩余 {len(df)} 条记录")
    
    # 转换为字典列表
    data = df.to_dict('records')

    # 递归处理所有NaN值
    def replace_nan(obj):
        if isinstance(obj, float) and pd.isna(obj):
            return None
        elif isinstance(obj, dict):
            return {k: replace_nan(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [replace_nan(i) for i in obj]
        else:
            return obj

    data = replace_nan(data)

    # 生成输出文件名
    if not output_file:
        path = Path(file_path)
        output_file = "/home/<USER>/Xinwu/data_PCV.json"

    # 保存为JSON文件
    print(f"正在保存为JSON文件: {output_file}")
    with open(output_file, 'w', encoding=encoding) as f:
        json.dump(data, f, ensure_ascii=False, indent=4)
    
    print(f"转换完成！JSON文件已保存为: {output_file}")
    return output_file

if __name__ == "__main__":
    file_path = "/home/<USER>/Xinwu/lathan/data_PCV.xlsx"
    xlsx_to_json(file_path)
