import json
import random
import os

def random_sample_cases(data_file, n, output_file=None):
    """
    从数据文件中随机抽取n条案例的案号
    
    Args:
        data_file: 数据文件路径
        n: 抽取的数量
        output_file: 输出文件路径，如果为None则自动生成
    """
    try:
        # 读取数据文件
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"总共有 {len(data)} 条数据")
        
        # 检查n是否超过总数据量
        if n > len(data):
            print(f"警告: 抽取数量 {n} 超过总数据量 {len(data)}，将抽取全部数据")
            n = len(data)
        
        # 随机抽取n条数据
        sampled_data = random.sample(data, n)
        
        # 提取案号
        case_numbers = []
        for item in sampled_data:
            if "案号" in item:
                case_numbers.append(item["案号"])
            else:
                print(f"警告: 某条数据缺少案号字段: {item}")
        
        # 生成输出文件名
        if output_file is None:
            output_file = f"random_sample_{n}_cases.txt"
        
        # 写入txt文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for case_num in case_numbers:
                f.write(case_num + '\n')
        
        print(f"成功抽取 {len(case_numbers)} 个案号")
        print(f"案号已保存到: {output_file}")
        
        return case_numbers
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 {data_file}")
        return None
    except json.JSONDecodeError:
        print(f"错误: 文件 {data_file} 不是有效的JSON格式")
        return None
    except Exception as e:
        print(f"错误: {e}")
        return None

def main():
    """主函数"""
    # 默认数据文件路径
    data_file = "data_PCV.json"
    
    # 检查文件是否存在
    if not os.path.exists(data_file):
        print(f"错误: 找不到数据文件 {data_file}")
        print("请确保数据文件在当前目录下")
        return
    
    # 获取用户输入
    try:
        n = int(input("请输入要抽取的案例数量: "))
        if n <= 0:
            print("错误: 抽取数量必须大于0")
            return
    except ValueError:
        print("错误: 请输入有效的数字")
        return
    
    # 询问是否自定义输出文件名
    custom_name = input("是否自定义输出文件名？(y/n，默认为n): ").lower()
    output_file = None
    if custom_name == 'y':
        output_file = input("请输入输出文件名(包含.txt后缀): ")
        if not output_file.endswith('.txt'):
            output_file += '.txt'
    
    # 执行抽取
    case_numbers = random_sample_cases(data_file, n, output_file)
    
    if case_numbers:
        print("\n抽取的案号预览(前10个):")
        for i, case_num in enumerate(case_numbers[:10]):
            print(f"{i+1}. {case_num}")
        if len(case_numbers) > 10:
            print(f"... 还有 {len(case_numbers) - 10} 个案号")

if __name__ == "__main__":
    main()
