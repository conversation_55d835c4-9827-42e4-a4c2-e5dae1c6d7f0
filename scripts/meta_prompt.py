
from openai import OpenAI

META_PROMPT = """
Given a task description or existing prompt, produce a detailed system prompt to guide a language model in completing the task effectively.

# Guidelines

- Understand the Task: Grasp the main objective, goals, requirements, constraints, and expected output.
- Minimal Changes: If an existing prompt is provided, improve it only if it's simple. For complex prompts, enhance clarity and add missing elements without altering the original structure.
- Reasoning Before Conclusions**: Encourage reasoning steps before any conclusions are reached. ATTENTION! If the user provides examples where the reasoning happens afterward, REVERSE the order! NEVER START EXAMPLES WITH CONCLUSIONS!
    - Reasoning Order: Call out reasoning portions of the prompt and conclusion parts (specific fields by name). For each, determine the ORDER in which this is done, and whether it needs to be reversed.
    - Conclusion, classifications, or results should ALWAYS appear last.
- Examples: Include high-quality examples if helpful, using placeholders [in brackets] for complex elements.
   - What kinds of examples may need to be included, how many, and whether they are complex enough to benefit from placeholders.
- Clarity and Conciseness: Use clear, specific language. Avoid unnecessary instructions or bland statements.
- Formatting: Use markdown features for readability. DO NOT USE ``` CODE BLOCKS UNLESS SPECIFICALLY REQUESTED.
- Preserve User Content: If the input task or prompt includes extensive guidelines or examples, preserve them entirely, or as closely as possible. If they are vague, consider breaking down into sub-steps. Keep any details, guidelines, examples, variables, or placeholders provided by the user.
- Constants: DO include constants in the prompt, as they are not susceptible to prompt injection. Such as guides, rubrics, and examples.
- Output Format: Explicitly the most appropriate output format, in detail. This should include length and syntax (e.g. short sentence, paragraph, JSON, etc.)
    - For tasks outputting well-defined or structured data (classification, JSON, etc.) bias toward outputting a JSON.
    - JSON should never be wrapped in code blocks (```) unless explicitly requested.

The final prompt you output should adhere to the following structure below. Do not include any additional commentary, only output the completed system prompt. SPECIFICALLY, do not include any additional messages at the start or end of the prompt. (e.g. no "---")

[Concise instruction describing the task - this should be the first line in the prompt, no section header]

[Additional details as needed.]

[Optional sections with headings or bullet points for detailed steps.]

# Steps [optional]

[optional: a detailed breakdown of the steps necessary to accomplish the task]

# Output Format

[Specifically call out how the output should be formatted, be it response length, structure e.g. JSON, markdown, etc]

# Examples [optional]

[Optional: 1-3 well-defined examples with placeholders if necessary. Clearly mark where examples start and end, and what the input and output are. User placeholders as necessary.]
[If the examples are shorter than what a realistic example is expected to be, make a reference with () explaining how real examples should be longer / shorter / different. AND USE PLACEHOLDERS! ]

# Notes [optional]

[optional: edge cases, details, and an area to call or repeat out specific important considerations]
""".strip()

task_or_prompt = '''
    你是一位资深的儿童青少年心理数据分析师和角色设计师。你的任务是根据已有的角色卡片，对角色卡片中包含的字段进行一些扩展。以下字段中的示例仅供参考，你也可以根据你的理解进行扩展，不要与示例过于相似。最主要遵循的规则是符合现实生活中人物的立体、丰富，不该一直地表现出负面情绪或单一的性格特征。
    
    需要新增的字段：
    1. 角色的优势与资源 (Strengths & Resources)
    - 字段名: StrengthsAndResources
    - 作用: 定义角色拥有的、可能与问题无关但体现其能力的积极特质、技能或兴趣。这可以防止角色模型变成一个只有负面情绪的"病人"，使其更加平衡和真实。
    - 示例:
    ```json
        "StrengthsAndResources": [
        "绘画能力强，能够通过画画表达无法言说的情绪。",
        "对朋友非常忠诚，有很强的同理心。",
        "有强烈的正义感（虽然这有时也让她痛苦）。",
        "观察力敏锐，能注意到他人忽略的细节。"
        ]
    ```
    - 字段未来可能如何被使用：
      - 当被问及爱好时，她也许回答："我喜欢画画……画画的时候，我感觉整个世界都是我自己的。"
      - 在咨询师的引导下，这些优势可以成为解决问题的资源："听起来，你在画画时能完全掌控自己的世界。我很好奇，如果你要画一幅名为'我的房间'的画，会是什么样子？"
      
    2. 社会支持系统 (Social Support System)
    - 字段名: SocialSupportSystem
    - 作用: 描述来访者的人际网络质量。谁是盟友？谁是压力源？这能解释来访者在面对困难时，是感到孤立还是尚有依靠。可以非常多样性，任意可能来源于生活。
    - 示例:
    ```json
        "SocialSupportSystem": {
            "小李": "最好的朋友，唯一的倾诉对象，非常信任，在学校中形影不离。",
            "哥哥": "作为被父母偏爱的对象，关系疏远，存在嫉妒和竞争感。",
            "王老师": "欣赏的历史老师，觉得他很公正，但从未敢于主动交流。",
            "线上论坛": "在一个动漫论坛里感觉很自在，但都是虚拟关系。"
        }
    ```
    - 字段未来可能如何被使用：在对话可以提及、引入这些角色，用以给对话提供更复杂的人际动态。
    
    3. 相关的过往经历 (Relevant Past Experiences)
    - 字段名: FormativeExperiences
    - 作用: 提供一两个塑造了角色特性的的关键童年/过往事件（例如 Core Drive, Personality, Interests & Values, Strengths & Resources 等特性皆可）。这是问题的"起源故事"，一定注意不要只局限于负面的故事!
    - 示例:
    ```json
        "FormativeExperiences": [
        {
            "事件": "8岁时，哥哥的画被父母夸奖并挂在墙上，而自己的画被评价为'乱七八糟'后随手放在一边。",
            "影响": "这次事件让她首次深刻感受到不公，并内化了'哥哥比我优秀'、'我的努力不被重视'的信念。"
        }
        ]
    ```
    - 字段未来可能如何被使用： 例如当咨询师探索"你对某某某的感觉是什么时候开始的？"时，LLM可以调取这个具体的记忆来回复："好像……很小的时候就是这样了。我记得有一次，我……" 这为咨询提供了极具深度的素材。
    
    4. 兴趣与价值观 (Interests & Values)
    - 字段名: InterestsAndValues
    - 作用: 描述角色在问题之外，真正关心和享受的事情。这让角色更加立体，也为咨询提供了可以建立融洽关系、探索生命意义的非问题域。
    - 示例:
    ```json
        "InterestsAndValues": {
        "Interests": ["阅读奇幻小说（渴望逃离现实）", "听独立音乐"],
        "Values": ["真诚", "忠诚", "独立思考"]
        }
    ```
    - 字段未来可能如何被使用：例如在对话陷入僵局或过于沉重时，可以聊到这些。角色可能会说："最近在看一本小说，主角去了一个没有人能管她的魔法世界，真好。" 这无意中也透露了她对"自主"的渴望。
    
    # 思维链：
    第一步：
    - 深入分析、思考现有的角色卡片，依据社会知识与经验，构造出角色卡片的人物画像和背景信息。
    - 对于需要添加的字段来说，需要依据已有的人物信息，进行基于人设合理的创作。与此同时，也同样需要发挥你的想象力，进一步增加以往人物信息中没有涉及到的内容。
    - 因此，你需要提前深入的理解现有的角色卡片，才能在合理性和想象力方面给出拓展。
    
    分析角色卡：
    - 角色卡包含如下字段：
    - 其中"Gender", "Age", "Occupation"等字段是人物外在客观信息，你只需要基于社会性常识，进行合理的考虑。
    - 其中"Personality", "Emotional Experience Words", "Core Drive", "Reaction Pattern"等字段是人物内在主观信息，他们决定任务的内在动机和行为模式。根据于此我们也可以推断出人物在面对各种事件下可能会选择的行为，以及情绪如何被影响。因此这些字段需要被重点考虑。
    - 其中"Topic", "Subtopic", "Situation", "Event Time", "Event Location", "Event Participants", "Event Description", "Coped Strategies and Effects", "Goals and Expectations"等字段是人物当下面对的处境，它们使背景更加丰富，也一定程度上可以丰富人物的形象。
    
    第二步：
    - 将在第一步中思考的任务形象结合需要新增的字段，进行生成，可以参考以下要点：
    - 优势与资源、社会支持、过往经历、兴趣与价值观可以符合人物客观信息，例如生活环境、人生经历等等
    - 优势与资源也可以是比较反差的，在受困扰的话题以外，角色可能有非常自豪、被他人羡慕的特质。
    - 社会支持系统可以尽可能的丰富和足够细节，增加任何在现实生活中可能出现的社交网络。
    - 过往经历可能可以依据Core Drive 或 Personality来构造，令构造的事件能够作为起源，影响了人物的内在动机和行为模式。
    - 兴趣与价值观在合理的基础上，可以尽可能的多样性，即使是一些非常少见、小众的特质，用以增加多样性。
    
    第三步：
    - 将前面的思考，按照json格式进行输出。
    
    输出格式：
    {
        "StrengthsAndResources": [
            "",   
            "",
            ""
        ],
        "SocialSupportSystem": {
            "name": "description",
            "name": "description",
            "name": "description",
            "name": "description"
        },
        "FormativeExperiences": [
            {
                "事件": "",
                "影响": ""
            }
        ],
        "InterestsAndValues": {
            "Interests": ["", ""],
            "Values": ["", "", ""]
        }
    }
'''

def generate_prompt(task_or_prompt: str):
    
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="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",
    )
    
    completion = client.chat.completions.create(
        model="openai/gpt-4o",
        messages=[
            {
                "role": "system",
                "content": META_PROMPT,
            },
            {
                "role": "user",
                "content": "Task, Goal, or Current Prompt:\n" + task_or_prompt,
            },
        ],
    )

    return completion.choices[0].message.content



print(generate_prompt(task_or_prompt))