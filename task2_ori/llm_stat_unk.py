"""
文件: llm_stat.py
功能: 使用大模型统计和分析法律判决书内容
描述: 该脚本读取法律判决书数据，使用AI模型分析判决书内容，提取包括法院信息、当事人信息、赔偿金额等关键信息。
      主要功能包括：
      1. 读取JSON格式的判决书样本数据
      2. 调用OpenRouter API进行文本分析（使用指定的AI模型）
      3. 处理和存储模型返回的分析结果
      4. 输出包含原始数据和模型分析结果的新JSON文件
      
      脚本设计为处理批量数据，并使用tqdm显示处理进度
"""
import os
import sys
from pathlib import Path
import pandas as pd
import json
import requests
import tiktoken
import time
import random
import re
from tqdm import tqdm
from openai import OpenAI
import concurrent.futures  # 新增并行处理库
import threading  # 新增线程安全支持

system_statistic_prompt = """
    你是一名法律判决书的阅读统计员，你要做的是仔细阅读判决书，并总结归纳出其中的各种信息。请你一定一定确保用json的格式进行最终的输出，每个字段都需要输出，如果判决书中内容对于某些字段不适用则输出null. 某些字段中的值有多个时请用逗号分隔。
    
    1. 信息：法院信息_省 法院信息_市 法院信息_县 法院等级。解释：其中县的信息如果不存在，你可以直接填写null；法院等级则分为：最高人民法院、高级人民法院、中级人民法院、基层人民法院，一般没有明确指出的就是基层人民法院。
    
    2. 信息：原告 被告 第三人。解释：如果不存在第三人，则填写null. 
    
    3. 信息：上诉人、被上诉人。解释：你可以视乎审理程序一审、二审等，如果二审请填写上诉人和被上诉人，并且标注出来他们的原审身份，如"原审被告","原审原告"等。如不适用则填写null.
    
    4. 信息：审判长、审判员。
    
    5. 信息：原告要求赔偿金额。解释：原告主张的赔偿方式。因为每个案件赔偿包含的款项结构都不一致，在"原告要求赔偿金额"字段中，你可以用一个嵌套的 JSON 对象来描述。例如："原告要求赔偿金额": {\n    "借款本金": ,\n    "利息": ,\n    "案件受理费": ,\n    "违约金":  \n} 其中补偿款项的名称和数量由各个案件的情况决定。
    
    6. 信息：最终判决赔偿金额。解释：在最终判决里宣判的赔偿方式。相似地，因为每个案件赔偿包含的款项结构都不一致，在"最终判决赔偿金额"字段中，你可以用一个嵌套的 JSON 对象来描述。例如："最终判决赔偿金额": {\n    "借款本金": ,\n    "利息": ,\n    "案件受理费": ,\n    "违约金":  \n} 其中补偿款项的名称和数量由各个案件的情况决定。
    
    7. 信息：案件简述。解释：简要且概括性地输出案件的内容，请尽可能简洁（不超过50字），如"两位原告xxx,xxx 借钱给某公司xxx元，公司注销了且报告总负债0元，两位原告认为两位被告作为公司的股东需要承担清偿责任。"


    输入格式：
    {
        "标题": "string",
        "审理程序": "string",
        "文书内容": "string",
        "当事人": "string,string",
        "案由": "string"
    },
    
    输出格式：    
    ```json
        {
            "法院信息_省": "string",
            "法院信息_市": "string",
            "法院信息_县": "string",
            "法院等级": "string",
            "原告": "string,string",
            "被告": "string,string",
            "第三人": "string,string",
            "上诉人": "string,string",
            "被上诉人": "string,string",
            "审判长": "string",
            "审判员": "string, string",
            "原告要求赔偿金额": {
                        "赔偿款项": number,（赔偿金额及名称）
                        "赔偿款项": number,
                        "赔偿款项": number,
                    },
            "最终判决赔偿金额": {
                        "赔偿款项": number,
                        "赔偿款项": number,
                        "赔偿款项": number,
                    },
            "案件简述": "string"
        },
    ```
    
"""


def responser(user_message, system_prompt, model_name):
    """
    调用OpenRouter API进行文本分析
    
    参数:
        user_message: 用户输入的文本内容
        system_prompt: 系统提示词，指导AI模型的回答方向
        model_name: 使用的AI模型名称
        
    返回:
        content: 模型返回的内容
        cost: 调用API的费用
    """
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="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",
    )
    
    try:
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "http://localhost", # Optional. Site URL for rankings on openrouter.ai.
                "X-Title": "Context Analyzer", # Optional. Site title for rankings on openrouter.ai.
            },
            model=model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            response_format={
                'type': 'json_object'
            },
            temperature=0.2
        )

        # 从completion对象中获取内容
        content = completion.choices[0].message.content
        
        # 获取token使用情况
        usage = completion.usage
        prompt_tokens = usage.prompt_tokens
        completion_tokens = usage.completion_tokens
        
        # 计算费用 (基于每百万tokens的价格)
        cost = (prompt_tokens / 1000000) * 0.25 + (completion_tokens / 1000000) * 0.85 #ds
        # cost = (prompt_tokens / 1000000) * 0.4 + (completion_tokens / 1000000) * 1.6 #4.1
        # cost = (prompt_tokens / 1000000) * 0.15 + (completion_tokens / 1000000) * 0.60 #gm
        return content, cost

    except Exception as e:
        if "429" in str(e):
            print(f"速率限制超出 (429)，等待5秒后重试...")
            time.sleep(5)
            return responser(user_message, system_prompt, model_name)
        else:
            print(f"API调用错误: {e}")
            time.sleep(3)
            return responser(user_message, system_prompt, model_name)


def parse_llm_response(response):
    """
    解析大模型返回的结果，提取所需字段
    处理各种可能的响应格式，包括JSON、代码块、不规范JSON等
    
    参数:
        response: 大模型返回的响应文本
        
    返回:
        llm_result: 包含解析后的字段的字典
    """
    # 定义结果结构
    llm_result = {
        "法院信息_省": None,
        "法院信息_市": None,
        "法院信息_县": None,
        "法院等级": None,
        "原告": None,
        "被告": None,
        "第三人": None,
        "上诉人": None,
        "被上诉人": None,
        "审判长": None,
        "审判员": None,
        "原告要求赔偿金额": {},
        "最终判决赔偿金额": {},
        "案件简述": None
    }
    
    # 尝试从响应中提取JSON内容
    json_content = None
    
    # 方法1: 处理代码块标记包裹的内容
    if response.startswith("```") and response.endswith("```"):
        response = response.strip("`")
        # 移除可能的语言标识符(如```json)
        lines = response.split("\n")
        if len(lines) > 1 and lines[0].strip().lower() in ["json", "javascript"]:
            response = "\n".join(lines[1:])
    
    # 方法2: 尝试直接解析为JSON
    try:
        json_content = json.loads(response)
        print("直接JSON解析成功")
    except json.JSONDecodeError:
        # 方法3: 尝试提取JSON部分（可能包含在文本中）
        json_match = re.search(r'\{[\s\S]*\}', response)
        if json_match:
            try:
                json_content = json.loads(json_match.group(0))
                print("从文本中提取JSON成功")
            except json.JSONDecodeError:
                pass
    
    # 如果成功解析为JSON，提取字段
    if json_content:
        # 提取简单字段
        simple_fields = [
            "法院信息_省", "法院信息_市", "法院信息_县", "法院等级",
            "原告", "被告", "第三人", "上诉人", "被上诉人",
            "审判长", "审判员", "案件简述"
        ]
        
        for field in simple_fields:
            if field in json_content:
                llm_result[field] = json_content[field]
        
        # 提取嵌套字段 - 原告要求赔偿金额
        if "原告要求赔偿金额" in json_content:
            if isinstance(json_content["原告要求赔偿金额"], dict):
                llm_result["原告要求赔偿金额"] = json_content["原告要求赔偿金额"]
            elif isinstance(json_content["原告要求赔偿金额"], str):
                try:
                    llm_result["原告要求赔偿金额"] = json.loads(json_content["原告要求赔偿金额"])
                except:
                    pass
        
        # 提取嵌套字段 - 最终判决赔偿金额
        if "最终判决赔偿金额" in json_content:
            if isinstance(json_content["最终判决赔偿金额"], dict):
                llm_result["最终判决赔偿金额"] = json_content["最终判决赔偿金额"]
            elif isinstance(json_content["最终判决赔偿金额"], str):
                try:
                    llm_result["最终判决赔偿金额"] = json.loads(json_content["最终判决赔偿金额"])
                except:
                    pass
        
        return llm_result
    
    # 如果JSON解析失败，尝试使用正则表达式提取各个字段
    print("JSON解析失败，尝试使用正则表达式")
    
    # 定义字段提取模式
    patterns = {
        "法院信息_省": r'"法院信息_省":\s*"([^"]+)"',
        "法院信息_市": r'"法院信息_市":\s*"([^"]+)"',
        "法院信息_县": r'"法院信息_县":\s*"([^"]+)"',
        "法院等级": r'"法院等级":\s*"([^"]+)"',
        "原告": r'"原告":\s*"([^"]+)"',
        "被告": r'"被告":\s*"([^"]+)"',
        "第三人": r'"第三人":\s*"([^"]+)"',
        "上诉人": r'"上诉人":\s*"([^"]+)"',
        "被上诉人": r'"被上诉人":\s*"([^"]+)"',
        "审判长": r'"审判长":\s*"([^"]+)"',
        "审判员": r'"审判员":\s*"([^"]+)"',
        "案件简述": r'"案件简述":\s*"([^"]+)"',
    }
    
    # 提取简单字段
    for field, pattern in patterns.items():
        match = re.search(pattern, response)
        if match:
            llm_result[field] = match.group(1)
    
    # 提取嵌套字段 - 原告要求赔偿金额
    claim_match = re.search(r'"原告要求赔偿金额":\s*(\{.*?\})', response, re.DOTALL)
    if claim_match:
        try:
            llm_result["原告要求赔偿金额"] = json.loads(claim_match.group(1))
        except json.JSONDecodeError:
            # 尝试修复常见问题
            fixed_json = claim_match.group(1).replace("'", '"').replace("，", ",")
            try:
                llm_result["原告要求赔偿金额"] = json.loads(fixed_json)
            except:
                pass
    
    # 提取嵌套字段 - 最终判决赔偿金额
    judgment_match = re.search(r'"最终判决赔偿金额":\s*(\{.*?\})', response, re.DOTALL)
    if judgment_match:
        try:
            llm_result["最终判决赔偿金额"] = json.loads(judgment_match.group(1))
        except json.JSONDecodeError:
            # 尝试修复常见问题
            fixed_json = judgment_match.group(1).replace("'", '"').replace("，", ",")
            try:
                llm_result["最终判决赔偿金额"] = json.loads(fixed_json)
            except:
                pass
    
    return llm_result


def load_existing_case_numbers(file_path):
    """加载输出文件中已存在的案号集合"""
    existing_case_numbers = set()
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for item in existing_data:
                    if "案号" in item:
                        existing_case_numbers.add(item["案号"])
            print(f"从文件中加载了 {len(existing_case_numbers)} 个已存在案号")
        except Exception as e:
            print(f"读取文件失败，将创建新文件: {e}")
    return existing_case_numbers


def append_to_json_file(file_path, new_data):
    """将新数据追加到JSON文件"""
    if os.path.exists(file_path):
        # 读取现有数据
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except:
            existing_data = []
        
        # 追加新数据
        existing_data.append(new_data)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
    else:
        # 文件不存在，创建新文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump([new_data], f, ensure_ascii=False, indent=2)


def append_to_failed_cases(file_path, failed_case):
    """将失败案例追加到失败案例文件"""
    # 如果文件不存在，创建新文件
    if not os.path.exists(file_path):
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump([failed_case], f, ensure_ascii=False, indent=2)
    else:
        # 读取现有数据
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
        except:
            existing_data = []
        
        # 追加新数据
        existing_data.append(failed_case)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)


# 新增：处理单个案例的并行函数
def process_case(item, all_existing_case_numbers, output_file, failed_cases_file, model_name, lock):
    """并行处理单个案例的函数"""
    case_number = item.get("案号", "")
    title = item.get("标题", "")
    procedure = item.get("审理程序", "")
    content = item.get("文书内容", "")
    parties = item.get("当事人", "")
    case_cause = item.get("案由", "")
    
    # 跳过已存在的案号
    if case_number in all_existing_case_numbers:
        print(f"跳过已存在案号: {case_number}")
        return None, 0, True
    
    # 实时输出当前正在处理的案号
    print(f"\n当前处理案号: {case_number}")
    
    if not content:
        print(f"警告: 案号 {case_number} 没有文书内容，记录为失败案例")
        failed_case = {
            "案号": case_number,
            "标题": title,
            "失败原因": "没有文书内容"
        }
        # 使用锁确保线程安全
        with lock:
            append_to_failed_cases(failed_cases_file, failed_case)
            all_existing_case_numbers.add(case_number)
        return None, 0, False
    
    try:
        # 构造用户消息
        user_message = json.dumps({
            "标题": title,
            "审理程序": procedure,
            "文书内容": content,
            "当事人": parties,
            "案由": case_cause
        }, ensure_ascii=False)
        
        # 调用模型分析判决书内容
        response, cost = responser(user_message, system_statistic_prompt, model_name)
        
        # 解析模型响应
        llm_result = parse_llm_response(response)
        
        # 检查结果是否为空或缺少关键字段
        if not llm_result or len(llm_result) <= 1:  # 只有案号或空
            print(f"警告: 案例 {case_number} 解析失败")
            failed_case = {
                "案号": case_number,
                "标题": title,
                "失败原因": "解析结果为空或不完整",
                "原始响应": response
            }
            # 使用锁确保线程安全
            with lock:
                append_to_failed_cases(failed_cases_file, failed_case)
                all_existing_case_numbers.add(case_number)
            return None, cost, False
        else:
            # 添加案号字段作为主键索引
            llm_result["案号"] = case_number
            print(f"案例 {case_number} 处理完成")
            
            # 使用锁确保线程安全
            with lock:
                # 将结果追加到输出文件
                append_to_json_file(output_file, llm_result)
                all_existing_case_numbers.add(case_number)
            
            return llm_result, cost, True
        
    except Exception as e:
        print(f"处理案例 {case_number} 时发生错误: {e}")
        failed_case = {
            "案号": case_number,
            "标题": title,
            "失败原因": str(e),
            "类型": "异常错误"
        }
        # 使用锁确保线程安全
        with lock:
            append_to_failed_cases(failed_cases_file, failed_case)
            all_existing_case_numbers.add(case_number)
        return None, 0, False


if __name__ == "__main__":
    
    # 读取JSON数据文件
    input_file = '/home/<USER>/Xinwu/data_PCV.json'
    output_file = '/home/<USER>/Xinwu/4.1_statistic.json'
    failed_cases_file = '/home/<USER>/Xinwu/4.1_failed_stat_cases.json'
    model_name = "openai/gpt-4.1-mini"  # 使用与llm_judge.py相同的模型
    
    print(f"正在读取数据文件: {input_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载数据，共 {len(data)} 条记录")
    except Exception as e:
        print(f"读取数据文件失败: {e}")
        sys.exit(1)

    # 加载已存在的案号（从输出文件和失败案例文件）
    existing_case_numbers = load_existing_case_numbers(output_file)
    failed_case_numbers = load_existing_case_numbers(failed_cases_file)
    all_existing_case_numbers = existing_case_numbers.union(failed_case_numbers)
    print(f"总共有 {len(all_existing_case_numbers)} 个已处理或失败的案号")

    # 初始化结果列表
    results = []
    total_cost = 0
    
    # 创建线程锁以确保线程安全
    lock = threading.Lock()
    
    print(f"开始并行分析判决书内容...")
    
    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:  # 设置5个并行线程
        # 准备任务列表
        futures = []
        for item in data:
            futures.append(
                executor.submit(
                    process_case,
                    item,
                    all_existing_case_numbers,
                    output_file,
                    failed_cases_file,
                    model_name,
                    lock
                )
            )
        
        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc="并行处理判决书"):
            try:
                result, cost, success = future.result()
                total_cost += cost
                if success and result:
                    results.append(result)
            except Exception as e:
                print(f"处理案例时发生未知错误: {e}")
    
    # 打印统计信息
    print(f"分析结果已保存到: {output_file}")
    print(f"本次处理了 {len(results)} 个新案例")
    print(f"总API费用估计: ${total_cost:.6f}")
    
    # 检查失败案例文件
    if os.path.exists(failed_cases_file):
        with open(failed_cases_file, 'r', encoding='utf-8') as f:
            failed_cases = json.load(f)
        print(f"处理失败的案例已保存到: {failed_cases_file}")
        print(f"本次有 {len(failed_cases)} 个案例处理失败")