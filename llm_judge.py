"""
文件: llm_judge.py
功能: 使用大模型分析法律判决书内容并评估法院是否支持原告否认被告的法人人格
描述: 该脚本读取法律判决书数据，使用AI模型分析判决书内容，判断法院是否支持原告否认被告的法人人格。
      主要功能包括：
      1. 读取JSON格式的判决书样本数据
      2. 调用OpenRouter API进行文本分析（使用指定的AI模型）
      3. 处理和存储模型返回的分析结果
      4. 输出包含原始数据和模型分析结果的新JSON文件
      
      脚本设计为处理批量数据，并使用tqdm显示处理进度
"""
import os
import sys
from pathlib import Path
import pandas as pd
import json
import requests
import tiktoken
import time
import random
import re
from tqdm import tqdm
from openai import OpenAI
from prompt import system_judge_prompt
import concurrent.futures  # 新增并行处理库
import threading  # 新增线程安全支持


def responser(user_message, system_prompt, model_name):
    """
    调用OpenRouter API进行文本分析
    
    参数:
        user_message: 用户输入的文本内容
        system_prompt: 系统提示词，指导AI模型的回答方向
        model_name: 使用的AI模型名称
        
    返回:
        content: 模型返回的内容
        cost: 调用API的费用
    """
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="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",
    )
    
    try:
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "http://localhost", # Optional. Site URL for rankings on openrouter.ai.
                "X-Title": "Context Analyzer", # Optional. Site title for rankings on openrouter.ai.
            },
            model=model_name,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            response_format={
                'type': 'json_object'
            },
            temperature=0.2
        )

        # 从completion对象中获取内容
        content = completion.choices[0].message.content
        
        # 获取token使用情况
        usage = completion.usage
        prompt_tokens = usage.prompt_tokens
        completion_tokens = usage.completion_tokens
        
        # 计算费用 (基于每百万tokens的价格)
        # cost = (prompt_tokens / 1000000) * 0.25 + (completion_tokens / 1000000) * 0.85 #ds
        cost = (prompt_tokens / 1000000) * 0.4 + (completion_tokens / 1000000) * 1.6 #4.1
        # cost = (prompt_tokens / 1000000) * 0.15 + (completion_tokens / 1000000) * 0.60 #gm
        return content, cost

    except Exception as e:
        if "429" in str(e):
            print(f"速率限制超出 (429)，等待5秒后重试...")
            time.sleep(5)
            return responser(user_message, system_prompt, model_name)
        else:
            print(f"API调用错误: {e}")
            time.sleep(3)
            return responser(user_message, system_prompt, model_name)


def parse_llm_response(response):
    """
    解析大模型返回的结果，            # response_format={
            #     'type': 'json_object'
            # },
    处理可能被代码块标记```json ```包裹的响应
    
    参数:
        response: 大模型返回的响应文本
        
    返回:
        llm_result: 包含解析后的字段的字典
    """
    llm_result = {
        "是否存在被告滥用公司法人独立地位或股东有限责任": None,
        "滥用依据": None,
        "法院是否支持原告否认被告的法人人格": None,
        "否认人格依据": None
    }
    
    # 处理可能被代码块标记包裹的内容
    if response.startswith("```") and response.endswith("```"):
        response = response.strip("`")
        # 移除可能的语言标识符(如```json)
        lines = response.split("\n")
        if len(lines) > 1 and not lines[0].strip():
            response = "\n".join(lines[1:])
        elif len(lines) > 1 and lines[0].strip().lower() in ["json", "javascript"]:
            response = "\n".join(lines[1:])
    
    # 尝试解析JSON格式
    try:
        parsed_json = json.loads(response)
        
        # 提取四个字段
        if "是否存在被告滥用公司法人独立地位或股东有限责任" in parsed_json:
            llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"] = parsed_json["是否存在被告滥用公司法人独立地位或股东有限责任"]
        if "滥用依据" in parsed_json:
            llm_result["滥用依据"] = parsed_json["滥用依据"]
        if "法院是否支持原告否认被告的法人人格_llm" in parsed_json:  # 注意可能有_llm后缀
            support_value = parsed_json["法院是否支持原告否认被告的法人人格_llm"]
            # 验证字段值必须为"Y"或"N"
            if support_value in ["Y", "N"]:
                llm_result["法院是否支持原告否认被告的法人人格"] = support_value
        elif "法院是否支持原告否认被告的法人人格" in parsed_json:
            support_value = parsed_json["法院是否支持原告否认被告的法人人格"]
            # 验证字段值必须为"Y"或"N"
            if support_value in ["Y", "N"]:
                llm_result["法院是否支持原告否认被告的法人人格"] = support_value
        if "否认人格依据" in parsed_json:
            llm_result["否认人格依据"] = parsed_json["否认人格依据"]
            
        print("JSON格式解析成功")
        return llm_result
    except json.JSONDecodeError:
        print("JSON解析失败，尝试使用正则表达式")
        pass
    
    # 尝试使用正则表达式解析
    try:
        # 解析字段1：是否存在滥用
        abuse_match = re.search(r'"是否存在被告滥用公司法人独立地位或股东有限责任":\s*"([^"]+)"', response)
        if abuse_match:
            llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"] = abuse_match.group(1)
            
        # 解析字段2：滥用依据
        abuse_basis_match = re.search(r'"滥用依据":\s*"([^"]+)"', response)
        if abuse_basis_match:
            llm_result["滥用依据"] = abuse_basis_match.group(1)
        
        # 解析字段3："法院是否支持原告否认被告的法人人格"字段
        support_match = re.search(r'"法院是否支持原告否认被告的法人人格(?:_llm)?":\s*"([^"]+)"', response)
        if support_match:
            support_value = support_match.group(1)
            # 验证字段值必须为"Y"或"N"
            if support_value in ["Y", "N"]:
                llm_result["法院是否支持原告否认被告的法人人格"] = support_value
        
        # 解析字段4："否认人格依据"字段
        basis_match = re.search(r'"否认人格依据":\s*"([^"]+)"', response)
        if basis_match:
            llm_result["否认人格依据"] = basis_match.group(1)
            
        print("正则表达式解析成功")
        return llm_result
    except Exception as e:
        print(f"正则解析错误: {e}")
        return llm_result

# 新增：从txt文件读取案号列表
def load_case_numbers_from_txt(file_path):
    """
    从txt文件中读取案号列表
    
    参数:
        file_path: txt文件路径
        
    返回:
        case_numbers: 包含案号的列表
    """
    case_numbers = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                case_number = line.strip()
                if case_number:
                    case_numbers.append(case_number)
        print(f"从文件 {file_path} 中读取了 {len(case_numbers)} 个案号")
        return case_numbers
    except Exception as e:
        print(f"读取案号文件失败: {e}")
        return []


def load_existing_case_numbers(output_file):
    """加载输出文件和失败案例文件中已存在的案号集合"""
    existing_case_numbers = set()
    
    # 加载输出文件中的案号
    if os.path.exists(output_file):
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for item in existing_data:
                    if "案号" in item:
                        existing_case_numbers.add(item["案号"])
            print(f"从输出文件中加载了 {len(existing_data)} 个已存在案号")
        except Exception as e:
            print(f"读取输出文件失败: {e}")
    
    return existing_case_numbers


def append_to_json_file(file_path, new_data, lock=None):
    """将新数据追加到JSON文件"""
    # 使用锁确保线程安全
    if lock:
        lock.acquire()
    
    try:
        if os.path.exists(file_path):
            # 读取现有数据
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except:
                existing_data = []
            
            # 追加新数据
            existing_data.append(new_data)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
        else:
            # 文件不存在，创建新文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump([new_data], f, ensure_ascii=False, indent=2)
    finally:
        if lock:
            lock.release()


def append_to_failed_cases(file_path, failed_case, lock=None):
    """将失败案例追加到失败案例文件"""
    # 使用锁确保线程安全
    if lock:
        lock.acquire()
    
    try:
        # 如果文件不存在，创建新文件
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump([failed_case], f, ensure_ascii=False, indent=2)
        else:
            # 读取现有数据
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except:
                existing_data = []
            
            # 追加新数据
            existing_data.append(failed_case)
            
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
    finally:
        if lock:
            lock.release()


# 新增：处理单个案例的并行函数
def process_case(item, existing_case_numbers, output_file, failed_cases_file, model_name, lock):
    """并行处理单个案例的函数"""
    case_number = item.get("案号", "")
    content = item.get("文书内容", "")
    procedure = item.get("审理程序", "")  # 获取审理程序
    
    # 跳过已存在的案号
    if case_number in existing_case_numbers:
        print(f"跳过已存在案号: {case_number}")
        return None, 0, True
    
    # 实时输出当前正在处理的案号
    print(f"\n当前处理案号: {case_number}")
    
    if not content:
        print(f"警告: 案号 {case_number} 没有文书内容，记录为失败案例")
        failed_case = {
            "案号": case_number,
            "失败原因": "没有文书内容"
        }
        # 使用锁确保线程安全
        append_to_failed_cases(failed_cases_file, failed_case, lock)
        existing_case_numbers.add(case_number)
        return None, 0, False
    
    try:
        # 构造输入内容，包含审理程序和文书内容
        combined_content = f"审理程序：{procedure}\n文书内容：{content}"
        
        # 调用模型分析判决书内容
        response, cost = responser(combined_content, system_judge_prompt, model_name)
        
        # 解析模型响应
        llm_result = parse_llm_response(response)
        
        # 检查结果是否有效
        is_valid = (llm_result["法院是否支持原告否认被告的法人人格"] is not None and
                   llm_result["否认人格依据"] is not None and
                   llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"] is not None and
                   llm_result["滥用依据"] is not None)
        
        if not is_valid:
            print(f"警告: 案例 {case_number} 解析结果不完整，记录为失败案例")
            failed_case = {
                "案号": case_number,
                "失败原因": "解析结果不完整",
                "原始响应": response,
                "解析结果": llm_result
            }
            # 使用锁确保线程安全
            append_to_failed_cases(failed_cases_file, failed_case, lock)
            existing_case_numbers.add(case_number)
            return None, cost, False
        
        # 构造结果，不再包含文书内容和原始标注
        result = {
            "案号": case_number,
            "是否存在被告滥用公司法人独立地位或股东有限责任": llm_result["是否存在被告滥用公司法人独立地位或股东有限责任"],
            "滥用依据": llm_result["滥用依据"],
            "法院是否支持原告否认被告的法人人格": llm_result["法院是否支持原告否认被告的法人人格"],
            "否认人格依据": llm_result["否认人格依据"]
        }
        
        # 打印当前案例的处理结果
        print(f"结果: 是否存在滥用={llm_result['是否存在被告滥用公司法人独立地位或股东有限责任']}, 是否支持否认={llm_result['法院是否支持原告否认被告的法人人格']}")
        
        # 使用锁确保线程安全
        append_to_json_file(output_file, result, lock)
        existing_case_numbers.add(case_number)
        
        return result, cost, True
        
    except Exception as e:
        print(f"处理案例 {case_number} 时发生错误: {e}")
        failed_case = {
            "案号": case_number,
            "失败原因": str(e),
            "类型": "异常错误"
        }
        # 使用锁确保线程安全
        append_to_failed_cases(failed_cases_file, failed_case, lock)
        existing_case_numbers.add(case_number)
        return None, 0, False


if __name__ == "__main__":
    import argparse
    
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='分析法律判决书内容并评估法院是否支持原告否认被告的法人人格')
    parser.add_argument('--input', default='/home/<USER>/Xinwu/data_PCV.json', help='输入的JSON数据文件路径')
    parser.add_argument('--output', default='/home/<USER>/Xinwu/4.1_full_judge.json', help='输出结果文件路径')
    parser.add_argument('--failed', default='/home/<USER>/Xinwu/4.1_failed_judge_cases.json', help='失败案例记录文件路径')
    parser.add_argument('--model', default="deepseek/deepseek-chat-v3-0324", help='使用的AI模型名称')
    # openai/gpt-4.1-mini      deepseek/deepseek-chat-v3-0324
    parser.add_argument('-m', '--mode', choices=['all', 'txt'], default='all', help='处理模式：all表示处理所有案例，txt表示从文件读取指定案号')
    parser.add_argument('-c', '--case_file', help='案号文件路径，mode=txt时必须提供')
    
    args = parser.parse_args()
    
    # 设置文件路径和模型名称
    input_file = args.input
    output_file = args.output
    failed_cases_file = args.failed
    model_name = args.model
    
    print(f"正在读取数据文件: {input_file}")
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载数据，共 {len(data)} 条记录")
    except Exception as e:
        print(f"读取数据文件失败: {e}")
        sys.exit(1)
    
    # 加载已存在的案号（从输出文件和失败案例文件）
    existing_case_numbers = load_existing_case_numbers(output_file)
    print(f"总共有 {len(existing_case_numbers)} 个已处理或失败的案号")

    # 如果是txt模式，从文件读取案号列表
    target_cases = []
    if args.mode == 'txt':
        if not args.case_file:
            print("错误: mode=txt时必须提供case_file参数")
            sys.exit(1)
        
        # 加载指定案号列表
        case_numbers = load_case_numbers_from_txt(args.case_file)
        if not case_numbers:
            print("未从案号文件中读取到任何案号，程序退出")
            sys.exit(1)
        
        # 查找匹配的案例
        case_number_set = set(case_numbers)
        for item in data:
            if item.get("案号", "") in case_number_set:
                target_cases.append(item)
        
        print(f"在输入数据中找到 {len(target_cases)} 个匹配案例，将进行处理")
        
        # 如果没有找到任何匹配案例，则退出
        if not target_cases:
            print("未找到任何匹配的案例，程序退出")
            sys.exit(1)
    else:
        # all模式，处理所有案例
        target_cases = data

    # 初始化结果列表
    results = []
    total_cost = 0
    
    # 创建线程锁以确保线程安全
    lock = threading.Lock()
    
    print(f"开始并行分析判决书内容...")
    
    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:  # 设置5个并行线程
        # 准备任务列表
        futures = []
        for item in target_cases:
            futures.append(
                executor.submit(
                    process_case,
                    item,
                    existing_case_numbers,
                    output_file,
                    failed_cases_file,
                    model_name,
                    lock
                )
            )
        
        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc="并行处理判决书"):
            try:
                result, cost, success = future.result()
                total_cost += cost
                if success and result:
                    results.append(result)
            except Exception as e:
                print(f"处理案例时发生未知错误: {e}")
    
    # 打印统计信息
    print(f"分析结果已保存到: {output_file}")
    print(f"本次处理了 {len(results)} 个新案例")
    print(f"总API费用估计: ${total_cost:.6f}")
    
    # 检查失败案例文件
    if os.path.exists(failed_cases_file):
        with open(failed_cases_file, 'r', encoding='utf-8') as f:
            failed_cases = json.load(f)
        print(f"处理失败的案例已保存到: {failed_cases_file}")
        print(f"本次有 {len(failed_cases)} 个案例处理失败")