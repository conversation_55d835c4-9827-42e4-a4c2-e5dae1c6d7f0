"""
文件: llm_stat.py
功能: 使用大模型统计和分析法律判决书内容
描述: 该脚本读取法律判决书数据，使用AI模型分析判决书内容，提取包括法院信息、当事人信息、赔偿金额等关键信息。
      主要功能包括：
      1. 读取JSON格式的判决书样本数据
      2. 调用OpenRouter API进行文本分析（使用指定的AI模型）
      3. 处理和存储模型返回的分析结果
      4. 输出包含原始数据和模型分析结果的新JSON文件
      
      脚本设计为处理批量数据，并使用tqdm显示处理进度
"""
import os
import sys
from pathlib import Path
import pandas as pd
import json
import requests
import tiktoken
import time
import random
import re
from tqdm import tqdm
from openai import OpenAI
from prompt import *
import argparse  # 新增
import concurrent.futures  # 新增并行处理库
import threading  # 新增线程安全支持

# 读取JSON数据文件
INPUT_FILE = '/home/<USER>/Xinwu/data_PCV.json'
OUTPUT_FILE = '/home/<USER>/Xinwu/ds_retry_500.json'
FAILED_CASES_FILE = '/home/<USER>/Xinwu/ds_failed_cases.json'
SYS_PROMPT = system_fee_prompt
MODEL_NAME = "deepseek/deepseek-chat-v3-0324"  # 使用与llm_judge.py相同的模型
# openai/gpt-4.1-mini      deepseek/deepseek-chat-v3-0324

# parser.add_argument('-m', '--mode', choices=['all', 'txt'], default='all', help='处理模式：all表示处理所有案例，txt表示从文件读取指定案号')
# parser.add_argument('-c', '--case_file', help='案号文件路径，mode=txt时必须提供')

def parse_llm_response(response):
    """
    解析大模型返回的结果，提取所需字段
    处理可能被代码块标记```json ```包裹的响应
    
    参数:
        response: 大模型返回的响应文本
        
    返回:
        llm_result: 包含解析后的字段的字典
    """
    # 处理可能被代码块标记包裹的内容
    if response.startswith("```") and response.endswith("```"):
        response = response.strip("`")
        # 移除可能的语言标识符(如```json)
        lines = response.split("\n")
        if len(lines) > 1 and not lines[0].strip():
            response = "\n".join(lines[1:])
        elif len(lines) > 1 and lines[0].strip().lower() in ["json", "javascript"]:
            response = "\n".join(lines[1:])
    
    try:
        # 尝试解析JSON格式
        parsed_json = json.loads(response)
        return parsed_json
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return None


def responser(user_message, system_prompt, MODEL_NAME, max_retries=3, retry_delay=5):
    """
    调用OpenRouter API进行文本分析并解析响应
    
    参数:
        user_message: 用户输入的文本内容
        system_prompt: 系统提示词，指导AI模型的回答方向
        MODEL_NAME: 使用的AI模型名称
        max_retries: 最大重试次数
        retry_delay: 重试延迟时间（秒）
        
    返回:
        parsed_content: 解析后的JSON内容
        cost: 调用API的费用
    """
    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key="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",
    )
    
    retry_count = 0
    while retry_count < max_retries:
        try:
            # 调用API
            completion = client.chat.completions.create(
                extra_headers={
                    "HTTP-Referer": "http://localhost",
                    "X-Title": "Context Analyzer",
                },
                model=MODEL_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_message}
                ],
                response_format={'type': 'json_object'},
                temperature=0.2
            )

            # 获取原始响应内容
            raw_content = completion.choices[0].message.content
            
            # 解析响应
            parsed_content = parse_llm_response(raw_content)
            
                
            return raw_content, parsed_content
            
            # 如果解析失败，准备重试
            print(f"解析失败，准备重试 (尝试 {retry_count + 1}/{max_retries})")
            
        except Exception as e:
            if "429" in str(e):
                print(f"速率限制超出 (429)，等待{retry_delay}秒后重试...")
            else:
                print(f"API调用错误: {e}")
        
        # 等待后重试
        sleep_time = retry_delay + random.uniform(0, 2)  # 添加随机抖动避免同步重试
        time.sleep(sleep_time)
        retry_count += 1
    
    # 达到最大重试次数仍失败
    print(f"达到最大重试次数 {max_retries}，放弃调用")
    return raw_content, None, 0



def load_existing_case_numbers(file_path):
    """加载输出文件中已存在的案号集合"""
    existing_case_numbers = set()
    if os.path.exists(file_path):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                existing_data = json.load(f)
                for item in existing_data:
                    if "案号" in item:
                        existing_case_numbers.add(item["案号"])
            print(f"从文件中加载了 {len(existing_case_numbers)} 个已存在案号")
        except Exception as e:
            print(f"读取文件失败，将创建新文件: {e}")
    return existing_case_numbers


def append_to_json_file(file_path, new_data, lock=None):
    """将新数据追加到JSON文件"""
    # 使用锁确保线程安全
    if lock:
        lock.acquire()

    try:
        if os.path.exists(file_path):
            # 读取现有数据
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except:
                existing_data = []

            # 追加新数据
            existing_data.append(new_data)

            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
        else:
            # 文件不存在，创建新文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump([new_data], f, ensure_ascii=False, indent=2)
    finally:
        if lock:
            lock.release()


def append_to_failed_cases(file_path, failed_case, lock=None):
    """将失败案例追加到失败案例文件"""
    # 使用锁确保线程安全
    if lock:
        lock.acquire()

    try:
        # 如果文件不存在，创建新文件
        if not os.path.exists(file_path):
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump([failed_case], f, ensure_ascii=False, indent=2)
        else:
            # 读取现有数据
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except:
                existing_data = []

            # 追加新数据
            existing_data.append(failed_case)

            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)
    finally:
        if lock:
            lock.release()

# 新增：从txt文件读取案号列表
def load_case_numbers_from_txt(file_path):
    """
    从txt文件中读取案号列表
    
    参数:
        file_path: txt文件路径
        
    返回:
        case_numbers: 包含案号的列表
    """
    case_numbers = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                case_number = line.strip()
                if case_number:
                    case_numbers.append(case_number)
        print(f"从文件 {file_path} 中读取了 {len(case_numbers)} 个案号")
        return case_numbers
    except Exception as e:
        print(f"读取案号文件失败: {e}")
        return []


# 新增：处理单个案例的并行函数
def process_case(item, existing_case_numbers, output_file, failed_cases_file, model_name, system_prompt, lock):
    """并行处理单个案例的函数"""
    case_number = item.get("案号", "")
    title = item.get("标题", "")
    procedure = item.get("审理程序", "")
    content = item.get("文书内容", "")
    parties = item.get("当事人", "")
    case_cause = item.get("案由", "")

    # 跳过已存在的案号
    if case_number in existing_case_numbers:
        print(f"跳过已存在案号: {case_number}")
        return None, 0, True

    # 实时输出当前正在处理的案号
    print(f"\n当前处理案号: {case_number}")

    if not content:
        print(f"警告: 案号 {case_number} 没有文书内容，记录为失败案例")
        failed_case = {
            "案号": case_number,
            "标题": title,
            "失败原因": "没有文书内容"
        }
        # 使用锁确保线程安全
        append_to_failed_cases(failed_cases_file, failed_case, lock)
        existing_case_numbers.add(case_number)
        return None, 0, False

    try:
        # 构造用户消息
        user_message = json.dumps({
            "标题": title,
            "审理程序": procedure,
            "文书内容": content,
            "当事人": parties,
            "案由": case_cause
        }, ensure_ascii=False)

        # 调用模型分析判决书内容
        response, llm_result = responser(
            user_message,
            system_prompt,
            model_name,
            max_retries=5,  # 最大重试次数
            retry_delay=3    # 重试间隔(秒)
        )

        # 检查结果是否为空或缺少关键字段
        if not llm_result or len(llm_result) <= 1:  # 只有案号或空
            print(f"警告: 案例 {case_number} 解析失败")
            failed_case = {
                "案号": case_number,
                "标题": title,
                "失败原因": "解析结果为空或不完整",
                "原始响应": response
            }
            # 使用锁确保线程安全
            append_to_failed_cases(failed_cases_file, failed_case, lock)
            existing_case_numbers.add(case_number)
            return None, cost, False

        # 添加案号字段作为主键索引
        llm_result["案号"] = case_number
        print(f"案例 {case_number} 处理完成")

        # 使用锁确保线程安全
        append_to_json_file(output_file, llm_result, lock)
        existing_case_numbers.add(case_number)

        return llm_result, cost, True

    except Exception as e:
        print(f"处理案例 {case_number} 时发生错误: {e}")
        failed_case = {
            "案号": case_number,
            "标题": title,
            "失败原因": str(e),
            "类型": "异常错误"
        }
        # 使用锁确保线程安全
        append_to_failed_cases(failed_cases_file, failed_case, lock)
        existing_case_numbers.add(case_number)
        return None, 0, False


if __name__ == "__main__":
    # 新增命令行参数解析
    parser = argparse.ArgumentParser(description='使用大模型统计和分析法律判决书内容')
    parser.add_argument('--input', default=INPUT_FILE, help='输入的JSON数据文件路径')
    parser.add_argument('--output', default=OUTPUT_FILE, help='输出结果文件路径')
    parser.add_argument('--failed', default=FAILED_CASES_FILE, help='失败案例记录文件路径')
    parser.add_argument('--model', default=MODEL_NAME, help='使用的AI模型名称')
    parser.add_argument('-m', '--mode', choices=['all', 'txt'], default='all', help='处理模式：all表示处理所有案例，txt表示从文件读取指定案号')
    parser.add_argument('-c', '--case_file', help='案号文件路径，mode=txt时必须提供')
    
    args = parser.parse_args()
    
    # 更新文件路径和模型名称
    INPUT_FILE = args.input
    OUTPUT_FILE = args.output
    FAILED_CASES_FILE = args.failed
    MODEL_NAME = args.model
    
    print(f"正在读取数据文件: {INPUT_FILE}")
    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"成功加载数据，共 {len(data)} 条记录")
    except Exception as e:
        print(f"读取数据文件失败: {e}")
        sys.exit(1)

    # 加载已存在的案号（从输出文件和失败案例文件）
    existing_case_numbers = load_existing_case_numbers(OUTPUT_FILE)
    failed_case_numbers = load_existing_case_numbers(FAILED_CASES_FILE)
    all_existing_case_numbers = existing_case_numbers.union(failed_case_numbers)
    print(f"总共有 {len(all_existing_case_numbers)} 个已处理或失败的案号")

    # 新增：如果是txt模式，从文件读取案号列表
    target_cases = []
    if args.mode == 'txt':
        if not args.case_file:
            print("错误: mode=txt时必须提供case_file参数")
            sys.exit(1)
        
        # 加载指定案号列表
        case_numbers = load_case_numbers_from_txt(args.case_file)
        if not case_numbers:
            print("未从案号文件中读取到任何案号，程序退出")
            sys.exit(1)
        
        # 查找匹配的案例
        case_number_set = set(case_numbers)
        for item in data:
            if item.get("案号", "") in case_number_set:
                target_cases.append(item)
        
        print(f"在输入数据中找到 {len(target_cases)} 个匹配案例，将进行处理")
        
        # 如果没有找到任何匹配案例，则退出
        if not target_cases:
            print("未找到任何匹配的案例，程序退出")
            sys.exit(1)
    else:
        # all模式，处理所有案例
        target_cases = data

    # 初始化结果列表
    results = []
    total_cost = 0

    # 创建线程锁以确保线程安全
    lock = threading.Lock()

    print(f"开始并行分析判决书内容...")

    # 使用线程池进行并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:  # 设置5个并行线程
        # 准备任务列表
        futures = []
        for item in target_cases:
            futures.append(
                executor.submit(
                    process_case,
                    item,
                    all_existing_case_numbers,
                    OUTPUT_FILE,
                    FAILED_CASES_FILE,
                    MODEL_NAME,
                    SYS_PROMPT,
                    lock
                )
            )

        # 使用tqdm显示进度
        for future in tqdm(concurrent.futures.as_completed(futures), total=len(futures), desc="并行处理判决书"):
            try:
                result, cost, success = future.result()
                total_cost += cost
                if success and result:
                    results.append(result)
            except Exception as e:
                print(f"处理案例时发生未知错误: {e}")
    
    # 打印统计信息
    print(f"分析结果已保存到: {OUTPUT_FILE}")
    print(f"本次处理了 {len(results)} 个新案例")
    print(f"总API费用估计: ${total_cost:.6f}")
    
    # 检查失败案例文件
    if os.path.exists(FAILED_CASES_FILE):
        with open(FAILED_CASES_FILE, 'r', encoding='utf-8') as f:
            failed_cases = json.load(f)
        print(f"处理失败的案例已保存到: {FAILED_CASES_FILE}")
        print(f"本次有 {len(failed_cases)} 个案例处理失败")