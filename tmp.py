import json
import argparse

def update_json_fields(source_file: str, target_file: str, output_file: str):
    """
    更新JSON文件中的特定字段
    
    参数:
        source_file: 包含更新数据的源JSON文件路径
        target_file: 需要更新的目标JSON文件路径
        output_file: 更新后的输出文件路径
    """
    # 读取源文件和目标文件
    try:
        with open(source_file, "r", encoding="utf-8") as f:
            source_data = json.load(f)
        
        with open(target_file, "r", encoding="utf-8") as f:
            target_data = json.load(f)
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 创建源文件的案号映射表
    source_map = {}
    for item in source_data:
        if "案号" in item:
            case_number = item["案号"]
            # 只保存需要更新的字段
            update_fields = {
                "原告要求赔偿金额": item.get("原告要求赔偿金额", {}),
                "最终判决赔偿金额": item.get("最终判决赔偿金额", {})
            }
            source_map[case_number] = update_fields
    
    # 记录更新的条目数和案号列表
    updated_count = 0
    updated_cases = []
    
    # 遍历目标文件中的条目进行更新
    for item in target_data:
        if "案号" in item:
            case_number = item["案号"]
            
            if case_number in source_map:
                # 获取源数据中需要更新的字段
                source_fields = source_map[case_number]
                
                # 记录是否更新了任何字段
                fields_updated = False
                
                # 更新原告要求赔偿金额
                if "原告要求赔偿金额" in source_fields:
                    item["原告要求赔偿金额"] = source_fields["原告要求赔偿金额"]
                    fields_updated = True
                
                # 更新最终判决赔偿金额
                if "最终判决赔偿金额" in source_fields:
                    item["最终判决赔偿金额"] = source_fields["最终判决赔偿金额"]
                    fields_updated = True
                
                # 如果有更新，记录案号
                if fields_updated:
                    updated_count += 1
                    updated_cases.append(case_number)
    
    # 保存更新后的目标文件
    try:
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(target_data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return
    
    # 输出更新结果
    print(f"总共更新了 {updated_count} 个条目")
    print("更新的案号列表:")
    for case in updated_cases:
        print(case)
    
    return updated_count, updated_cases

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='更新JSON文件中的特定字段')
    parser.add_argument('source', help='包含更新数据的源JSON文件路径')
    parser.add_argument('target', help='需要更新的目标JSON文件路径')
    parser.add_argument('output', help='更新后的输出文件路径')
    args = parser.parse_args()
    
    # 执行更新
    update_json_fields(args.source, args.target, args.output)